{"root": "app/src", "showErrorMessages": true, "allowedExtensions": ["js", "jsx", "ts", "tsx"], "breakLineAfterBlock": true, "includedFolders": ["_mock", "assets", "auth", "components", "custom-components", "contexts", "hooks", "lib", "layouts", "pages", "routes", "routes-config", "sections", "theme", "types", "utils"], "excludedFolders": [".git", ".husky", ".storybook", ".vite-cache", ".pnpm-store", "node_modules", "public", "build", "dist", "scripts"], "organizeOnSave": true, "applySpecialIdentifier": true, "specialIdentifier": "@", "replaceLongPathBySpecialIdentifier": false, "longPathLength": 3, "addSeparatorLineAfterImports": true, "separatorCharacter": "-", "separatorLineLength": 70, "addLibrariesToOthers": false, "othersBlockName": "others", "blocks": [{"name": "react", "displayName": false, "libraries": ["react", "prop-types"]}, {"name": "tanstack", "displayName": true, "libraries": ["@tanstack/react-router", "@tanstack/react-query", "@tanstack/*"]}, {"name": "utils", "displayName": true, "folders": ["utils", "lib/utils"], "libraries": ["react-helmet-async"]}, {"name": "@mui", "displayName": true, "libraries": ["@mui", "@mui/*"]}, {"name": "icons", "displayName": true, "folders": ["icons"], "libraries": ["@iconify/*"]}, {"name": "styles", "displayName": true, "folders": ["theme", "styles"], "libraries": ["styled-components", "styled-system"]}, {"name": "config", "displayName": true, "folders": ["config", "global-config"]}, {"name": "router", "displayName": true, "folders": ["router", "routes"]}, {"name": "api", "displayName": true, "folders": ["api", "queries", "mutations", "store"]}, {"name": "hoc", "displayName": true, "folders": ["hoc", "lib/hoc"]}, {"name": "hooks", "displayName": true, "folders": ["hooks"]}, {"name": "contexts", "displayName": true, "folders": ["contexts", "auth"]}, {"name": "constants", "displayName": true, "libraries": ["@env"], "folders": ["constants", "env", "lib/env"]}, {"name": "guards", "displayName": true, "folders": ["guards"]}, {"name": "functions", "displayName": true, "folders": ["functions"]}, {"name": "services", "displayName": true, "folders": ["services"]}, {"name": "locales", "displayName": true, "folders": ["i18n", "lib/i18n"]}, {"name": "layouts", "displayName": true, "folders": ["layouts"]}, {"name": "pages", "displayName": true, "folders": ["pages"]}, {"name": "views", "displayName": true, "folders": ["views"]}, {"name": "sections", "displayName": true, "folders": ["sections"]}, {"name": "components", "displayName": true, "folders": ["components", "custom-components"]}, {"name": "data", "displayName": true, "folders": ["data", "variables"]}, {"name": "mocks", "displayName": true, "folders": ["_mocks", "mocks", "_mock", "mock"]}, {"name": "assets", "displayName": true, "folders": ["assets", "asset", "medias", "media"]}, {"name": "testing", "displayName": true, "libraries": ["@testing-library/*", "cypress", "cypress-*", "vitest"], "folders": ["test", "testing"]}, {"name": "types", "displayName": true, "folders": ["_types", "types", "type"]}]}