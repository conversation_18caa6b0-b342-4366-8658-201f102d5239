*.local
dev
.fleet/

/app/src/pages/test

###> proteam/bundles/base/gitignore.front.txt ###
# DO NOT CHANGE IN .gitignore IT WILL BE OVERRIDED
# PLEASE CHANGE IN BUNDLES/BASE

# App
/app/dev
/app/public/env.js
!/app/.npmrc

## PWA
/app/public/mockServiceWorker.js
/app/public/worker-*.js*
/app/public/sw.js*
/app/public/workbox*

## Custom
/app/reports
/app/certs

# production
/app/build
/app/.cache/
/app/.next/
/app/out/

# ENVS MANAGEMENT
/app/.env.*.local

# dependencies
/**/node_modules
/**/package.lock
/**/yarn.lock
/**/npm-debug.log*
/**/yarn-debug.log*
/**/yarn-error.log*

# typescript
/**/*.tsbuildinfo
/**/.attach_*

.sentryclirc

# BASE FILES
/scripts/.base
/scripts/entrypoints/*.sh
/scripts/utils/*.sh
/containers/app/nginx/nginx_base.conf

# UTILS
!.gitkeep
.DS_Store
.DS_Store?
.idea/
.cz.yaml
*.pem
*.tar
docker-compose.override.yml

###< proteam/bundles/base/gitignore.front.txt ###

