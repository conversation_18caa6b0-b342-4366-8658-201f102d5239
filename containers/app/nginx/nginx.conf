server {
    root /build;
    index index.html;

    # Limit DDOS
    # limit_conn perip 100;

    include /etc/nginx/variables/nginx_variable.conf;

    error_page 404 /404.html;

    gzip on;
    gzip_vary on;
    gzip_min_length 10240;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css text/javascript application/x-javascript image/png image/jpeg;

    client_body_timeout 22;
    client_header_timeout 22;
    keepalive_timeout 22;
    send_timeout 20;

    location ~* \.map(\.js)?$ {
        if ($xsentry_token = "not value") {
            return 403;
        }
        # Vérifie que le header 'X-SENTRY-TOKEN' contient la valeur 'mon_token_secret'

        if ($http_x_sentry_token != $xsentry_token) {
            return 403;
        }

        # Si le token est bon, continue avec la racine normale
#         root root /build;
        try_files $uri =404;
    }

    include /etc/nginx/extends/nginx_base.conf;

}
