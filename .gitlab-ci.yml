include:
  - project: "tools/ci"
    ref: master
    file:
      - "ci/front.yml"
      - "utils/workflow.yml"

workflow:
  auto_cancel: !reference [.workflow-project, auto_cancel]
  rules:
    - !reference [.workflow-project, rules]
    - if: $CI_COMMIT_BRANCH == 'main'

stages:
  - release
  - package
  - notify
  - deploy
  - cleanup

variables:
  GIT_STRATEGY: clone
  GIT_DEPTH: "0"
  SENTRY_LOG_LEVEL: info
  NODE_OPTIONS: --max-old-space-size=5096

release:
  extends: .front:release

package:
  extends: .front:package

notify:
  extends: .front:notify

.deploy:
  stage: deploy
  extends: .use-deploy-swarm

deploy:
  stage: deploy
  extends: .use-deploy-swarm
  when: manual
  variables:
    IMG_TAG: $NEXT_VERSION
    SENTRY_STATE: "enabled"
    TRAEFIK_PREFIX: $APP_ENV
    TRAEFIK_ENTRYPOINT: internal
    DEPLOY_SERVER: 172.16.81.106
    DEPLOY_URL: $CI_PROJECT_NAME.$APP_ENV.$APP_STACK
    PUBLIC_PAWN_API_URL: "https://pawn.${APP_ENV}.${APP_STACK}"
    PUBLIC_MAGNET_API_URL: "https://magnet.${APP_ENV}.${APP_STACK}"
    PUBLIC_CELL_API_URL: "https://cell.${APP_ENV}.${APP_STACK}"
    PUBLIC_FIBER_API_URL: "https://fiber.${APP_ENV}.${APP_STACK}"
    XSENTRY_TOKEN: $XSENTRY_TOKEN
  rules:
    - if: $CI_COMMIT_REF_SLUG != 'main'
      when: never
    - if: $APP_ENV == 'dev'
      when: on_success
      variables:
        SENTRY_STATE: "disabled"
        DEPLOY_URL: $CI_PROJECT_NAME.internal.$APP_STACK
        DEPLOY_SERVER: 172.16.81.113
        PUBLIC_PAWN_API_URL: "https://pawn.internal.${APP_STACK}"
        PUBLIC_MAGNET_API_URL: "https://magnet.internal.${APP_STACK}"
        PUBLIC_CELL_API_URL: "https://cell.internal.${APP_STACK}"
        PUBLIC_FIBER_API_URL: "https://fiber.internal.${APP_STACK}"
    - if: $APP_ENV == 'production'
      when: manual
      variables:
        DEPLOY_URL: $CI_PROJECT_NAME.services.$APP_STACK
        PUBLIC_PAWN_API_URL: "https://pawn.services.${APP_STACK}"
        PUBLIC_MAGNET_API_URL: "https://magnet.services.${APP_STACK}"
        PUBLIC_CELL_API_URL: "https://cell.services.${APP_STACK}"
        PUBLIC_FIBER_API_URL: "https://fiber.services.${APP_STACK}"
    - when: manual # Allow any unmatched jobs
  environment:
    name: $APP_ENV
    url: $DEPLOY_URL
  parallel:
    matrix:
      - APP_ENV: ["dev", "pbe", "production"]
        APP_STACK: ["b2b.iliad.fr"]
