.ONESHELL:

help h:  ## Affiche l'aide d<PERSON><PERSON><PERSON><PERSON>
	@echo "Usage: make [target]" && \
	echo "" && \
	echo "Cibles disponibles :" && \
	echo "  update-cz		Update le .cz.yaml" && \
	echo "  down			Run appx down" && \
	echo "  prod			Run appx up en mode prod" && \
	echo "  build			Run le script build.sh"

update-cz u-cz:
	curl -sL https://gitlab.adm.internal.proteam.iliad.fr/bundles/base/-/raw/main/.cz.yaml > .cz.yaml

down d:
	appx down
prod p:
	appx up --prod --dynamic

build b:
	./scripts/build.sh
