# dependencies
/**/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/**/.next/

# local env files
.env.local
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

# environment variables
# .env
setEnv.sh
.pam_environment
*setEnv.sh
local_env

# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# IDEs and editors
*.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/launch.json

package-lock.json

### JetBrains template
# Covers JetBrains IDEs: IntelliJ, RubyMine, PhpStorm, AppCode, PyCharm, CLion, Android Studio, WebStorm and Rider
# Reference: https://intellij-support.jetbrains.com/hc/en-us/articles/206544839

# User-specific stuff

# CMake
cmake-build-*/

# File-based project format
*.iws


# mpeltonen/sbt-idea plugin
.idea_modules/

# JIRA plugin
atlassian-ide-plugin.xml

# Crashlytics plugin (for Android Studio and IntelliJ)
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
fabric.properties

## Custom
/report
/dev
/certs
/api_src

