#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';

// Supported naming conventions
const namingConventions = {
	camelCase: (str) => str.replace(/[-_ ](.)/g, (_, char) => char.toUpperCase()).replace(/^(.)/, (_, char) => char.toLowerCase()),
	kebabCase: (str) =>
		str
			.replace(/([a-z])([A-Z])/g, '$1-$2')
			.replace(/[_ ]/g, '-')
			.toLowerCase(),
	snake_case: (str) =>
		str
			.replace(/([a-z])([A-Z])/g, '$1_$2')
			.replace(/[- ]/g, '_')
			.toLowerCase(),
	PascalCase: (str) => str.replace(/(^\w|[-_ ]\w)/g, (match) => match.replace(/[-_ ]/, '').toUpperCase()),
	SCREAMING_SNAKE_CASE: (str) =>
		str
			.replace(/([a-z])([A-Z])/g, '$1_$2')
			.replace(/[- ]/g, '_')
			.toUpperCase()
};

const args = yargs(hideBin(process.argv))
	.scriptName('rename-files')
	.usage('$0 [options] <directory>')
	.option('convention', {
		alias: 'c',
		describe: 'Naming convention to use',
		choices: Object.keys(namingConventions),
		demandOption: true
	})
	.option('recursive', {
		alias: 'r',
		type: 'boolean',
		describe: 'Rename files recursively',
		default: false
	})
	.option('directories', {
		alias: 'd',
		type: 'boolean',
		describe: 'Rename directories as well',
		default: false
	})
	.option('quiet', {
		alias: 'q',
		type: 'boolean',
		describe: 'Suppress output',
		default: false
	})
	.option('dry-run', {
		type: 'boolean',
		describe: 'Perform a dry run without renaming files',
		default: false
	})
	.demandCommand(1, 'You must specify a directory to process')
	.help()
	.alias('help', 'h').argv;

// Configuration
const config = {
	targetDir: path.resolve(args._[0]),
	namingConvention: args.convention,
	recursive: args.recursive,
	includeDirs: args.directories,
	quiet: args.quiet,
	dryRun: args['dry-run']
};

// Utility function to rename files/directories
function renameItem(oldPath, newPath) {
	if (config.dryRun) {
		console.log(`Dry Run: Would rename "${oldPath}" to "${newPath}"`);
		return true;
	}
	try {
		if (oldPath.toLowerCase() === newPath.toLowerCase()) {
			const tempPath = `${oldPath}_temp`;
			fs.renameSync(oldPath, tempPath);
			fs.renameSync(tempPath, newPath);
		} else {
			fs.renameSync(oldPath, newPath);
		}
		if (!config.quiet) console.log(`Renamed: "${oldPath}" -> "${newPath}"`);
		return true;
	} catch (error) {
		console.error(`Error renaming "${oldPath}": ${error.message}`);
		return false;
	}
}

// Function to apply naming convention
function applyNamingConvention(name) {
	const ext = path.extname(name);
	const baseName = path.basename(name, ext);

	// Check for underscore prefix
	const hasUnderscore = baseName.startsWith('_');
	const nameToTransform = hasUnderscore ? baseName.slice(1) : baseName;

	const transform = namingConventions[config.namingConvention];
	const transformedName = transform(nameToTransform);

	// Restore underscore if needed
	const finalName = hasUnderscore ? `_${transformedName}` : transformedName;
	return finalName + ext;
}

// Recursive function to process files and directories
function processDirectory(dirPath) {
	const entries = fs.readdirSync(dirPath, { withFileTypes: true });

	entries.forEach((entry) => {
		const oldPath = path.join(dirPath, entry.name);
		const newName = applyNamingConvention(entry.name);
		const newPath = path.join(path.dirname(oldPath), newName);

		if (entry.isDirectory()) {
			if (config.includeDirs && oldPath !== newPath) {
				renameItem(oldPath, newPath);
			}

			// Always use actual current path when recursing
			const currentPath = fs.existsSync(newPath) ? newPath : oldPath;
			if (config.recursive) {
				processDirectory(currentPath);
			}
		} else if (entry.isFile() && oldPath !== newPath) {
			renameItem(oldPath, newPath);
		}
	});
}

// Main execution
try {
	if (!fs.existsSync(config.targetDir)) {
		throw new Error(`Directory "${config.targetDir}" does not exist.`);
	}

	processDirectory(config.targetDir);

	if (!config.quiet && !config.dryRun) {
		console.log('Renaming complete!');
	}
} catch (error) {
	console.error(`Error: ${error.message}`);
}
