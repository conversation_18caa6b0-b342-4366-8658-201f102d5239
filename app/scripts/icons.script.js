const fs = require('fs');
const path = require('path');
const _ = require('lodash');
const { exec } = require('child_process');

const iconsDirectoryPath = `public/assets/icons`;
const directoryPathIcons = path.join(__dirname, '../' + iconsDirectoryPath);
const iconsCpFilesPath = path.join(__dirname, `../src/components/ui/icon/iconsSvgComposant.ts`);
const files = fs.readdirSync(directoryPathIcons);
const pathIcons = [];

if (files && files.length > 0) {
	for (const file of files) {
		const fileSplit = file.split('.');
		const icon = _.startCase(fileSplit[0]).replace(/\s+/g, '') + `Icon`;
		pathIcons.push({
			icon,
			path: `${iconsDirectoryPath}/${file}`
		});
	}
}

pathIcons.forEach((items, index) => {
	const data = `export { default as ${items.icon} } from '${items.path}'\n`;
	if (index === 0) {
		fs.writeFileSync(iconsCpFilesPath, data);
	} else {
		fs.appendFileSync(iconsCpFilesPath, data);
	}
});

exec('pnpm prettier --write ' + iconsCpFilesPath, (error, stdout, stderr) => {
	if (error) {
		console.log(`error: ${error.message}`);
		return;
	}
	if (stderr) {
		console.log(`stderr: ${stderr}`);
	}
});
