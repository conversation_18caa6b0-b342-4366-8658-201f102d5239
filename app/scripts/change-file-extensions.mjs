import { readdir, rename } from 'fs/promises';
import { join, extname, basename } from 'path';

async function changeFileExtensions(folderPath) {
	try {
		const files = await readdir(folderPath);

		for (const file of files) {
			const filePath = join(folderPath, file);
			if (extname(file) === '.ts') {
				const newFilePath = join(folderPath, `${basename(file, '.ts')}.lazy.ts`);
				await rename(filePath, newFilePath);
				console.log(`Renamed: ${file} -> ${basename(newFilePath)}`);
			}
		}
	} catch (error) {
		console.error('Error:', error);
	}
}

const folderPath = '/path/to/your/folder'; // Replace with your folder path
changeFileExtensions(folderPath);
