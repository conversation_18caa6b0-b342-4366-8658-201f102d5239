const axios = require('axios')
const https = require('https')
const fs = require('fs')
const path = require('path')
// https://www.npmjs.com/package/n-readlines pour ligne fichier ligne par ligne
// https://www.npmjs.com/package/diff
// At request level
const agent = new https.Agent({
	rejectUnauthorized: false
})

let entities = {}

axios
	.get('https://localhost:21002/docs.jsonld', { httpsAgent: agent })
	.then((rep) => {
		// console.log(rep.data['hydra:supportedClass'][0])
		rep.data['hydra:supportedClass'].forEach((entity) => {
			if (
				entity['@id'] !== '#Entrypoint' &&
				entity['@id'] !== '#ConstraintViolation' &&
				entity['@id'] !== '#ConstraintViolationList'
			) {
				const name = entity['hydra:title']
				const data = {
					'@id': 'string'
				}
				entity['hydra:supportedProperty'].map((item) => {
					const propertyRange = item['hydra:property'].range

					switch (propertyRange) {
						case 'xmls:string':
						case 'xmls:dateTime':
							data[item['hydra:title']] = 'string'
							break
						case 'xmls:boolean':
							data[item['hydra:title']] = 'boolean'
							break
						case 'xmls:decimal':
						case 'xmls:integer':
							data[item['hydra:title']] = 'number'
							break
						default:
							data[item['hydra:title']] =
								propertyRange && propertyRange.includes('#') ? propertyRange.replace('#', 'I') : 'any'
					}

					// console.log(data)
				})

				entities[name] = data
			}
		})
		fs.writeFileSync('./api_src/entity.json', JSON.stringify(entities, null, 2))
		// console.log(entities)
		const entitiesToArray = Object.entries(entities)
		entitiesToArray.forEach(([key, dataKey]) => {
			const dataKeyToArray = Object.entries(dataKey)

			let textRaw = `export interface I${key} {\n`
			dataKeyToArray.forEach(([key2, dataKey2]) => {
				let keyVal = key2 === '@id' ? `'${key2}'` : key2
				switch (dataKey2) {
					case 'string':
						textRaw += '\t' + keyVal + ': string \n'
						break
					case 'boolean':
						textRaw += '\t' + keyVal + ': boolean \n'
						break
					case 'number':
						textRaw += '\t' + keyVal + ': number \n'
						break
					default:
						let dataTran = dataKey2[0] === 'I' ? `${dataKey2}\n` : 'any \n'
						textRaw += `\t${keyVal}: ${dataTran}`
				}
			})
			textRaw += '}'

			// fs.writeFileSync(`./api_src/${key}.interface.ts`, JSON.stringify(dataKey, null, 2))
			fs.writeFileSync(`./api_src/${key}.interface.ts`, textRaw)
		})
		console.log(Object.entries(entities)[0])
	})
	.catch((err) => {
		console.error(err)
	})
