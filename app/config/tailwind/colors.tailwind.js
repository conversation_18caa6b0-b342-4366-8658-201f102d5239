// const colors = require('tailwindcss/colors')

export default {
	safelist: [
		{
			amber: {
				100: '#FEF3C7',
				400: '#FBBF24',
				600: '#D97706'
			},
			black: '#262626',
			blue: {
				50: '#EFF6FF',
				100: '#E0F2FE',
				200: '#BFDBFE'
			},
			dark: {
				700: '#24272B',
				mode: '#0F1112'
			},
			emerald: {
				50: '#ECFDF5',
				200: '#A7F3D0',
				700: '#047857'
			},
			gray: {
				25: '#F9F9F9',
				50: '#F4F4F4',
				100: '#E0E0E0',
				200: '#C6C6C6',
				300: '#A8A8A8',
				400: '#8D8D8D',
				500: '#6F6F6F',
				600: '#525252',
				700: '#393939',
				800: '#262626',
				900: '#161616',
				DEFAULT: '#6F6F6F',
				transparent: 'rgba(254, 254, 254, 0.75)'
			},
			green: {
				DEFAULT: '#10B981',
				100: '#DCFCE7',
				validation: {
					light: '#E7F8F2'
				}
			},
			indigo: {
				200: '#C7D2FE',
				600: '#4F46E5',
				800: '#3730A3'
			},
			linear: {
				first: 'rgba(251, 114, 131, 0.4)',
				second: 'rgba(253, 185, 116, 0.4)'
			},
			opacity: {
				dark: {
					800: 'rgba(0, 0, 0, 0.8)'
				},
				light: {
					800: 'rgba(255, 255, 255, 0.8)'
				},
				white: {
					200: 'rgba(255, 255, 255, 0.1)'
				},
				gray: {
					750: 'rgba(254, 254, 254, 0.75)'
				}
			},
			orange: {
				100: '#FF8159',
				200: '#FED7AA',
				900: '#7C2D12'
			},
			pink: {
				100: '#FFE4E6'
			},
			purple: {
				50: '#F4F3FF',
				700: '#5925DC'
			},
			red: {
				50: '#FFE6E6',
				100: '#F5B0B0',
				200: '#EB7F7F',
				300: '#E05151',
				400: '#D62727',
				500: '#CC0000',
				600: '#B30000',
				700: '#990000',
				800: '#800000',
				900: '#660000',
				DEFAULT: '#CC0000'
			},
			state: {
				error: {
					50: '#FEF3F2',
					700: '#B42318',
					200: '#EB7F7F',
					DEFAULT: '#EF4444'
				},
				info: '#275ECB',
				success: {
					50: '#ECFDF5',
					700: '#047857',
					DEFAULT: '#10B981'
				},
				warning: {
					700: '#B54708',
					50: '#FFFBEB',
					200: '#FDE68A',
					DEFAULT: '#F59E0B'
				}
			},
			white: {
				DEFAULT: '#FFFFFF'
			}
		}
	]
};
