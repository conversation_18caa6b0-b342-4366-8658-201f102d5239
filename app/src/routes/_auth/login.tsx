// tanstack
import { createFileRoute, redirect } from '@tanstack/react-router';
// hoc
import { withErrorBoundarySentry } from '@/lib/hoc';
// sections
import { LoginView } from '@/sections/auth/login';

// ----------------------------------------------------------------------

type LoginSearch = {
	redirect?: string;
	registerSuccess?: string;
};

export const Route = createFileRoute('/_auth/login')({
	beforeLoad: ({ context: { auth } }) => {
		if (auth.user) {
			throw redirect({
				to: '/dashboard'
			});
		}
	},
	validateSearch: (search: Record<string, unknown>): LoginSearch => {
		return {
			redirect: (search?.redirect as string) || undefined,
			registerSuccess: (search?.registerSuccess as string) || undefined
		};
	},
	component: withErrorBoundarySentry(LoginView)
});
