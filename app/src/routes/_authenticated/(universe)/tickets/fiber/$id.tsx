// tanstack
import { useQuery } from '@tanstack/react-query';
import { createFileRoute } from '@tanstack/react-router';
// api
import { magnetQueryKeys } from '@/api/queries';
// hoc
import { withErrorBoundarySentry } from '@/lib/hoc';
// sections
import { TicketsFiberDetailsView } from '@/sections/universe/tickets/fiber';

// ----------------------------------------------------------------------

export const Route = createFileRoute('/_authenticated/(universe)/tickets/fiber/$id')({
	component: withErrorBoundarySentry(TechTicketPage)
});

function TechTicketPage() {
	const { id } = Route.useParams();

	const { data: ticketData } = useQuery({
		...magnetQueryKeys.magnet_ticket.byId(id)
	});

	return <TicketsFiberDetailsView ticket={ticketData} />;
}
