// tanstack
import { createFileRoute } from '@tanstack/react-router';
// hoc
import { withErrorBoundarySentry } from '@/lib/hoc';
// locales
import { SiteMap } from '@/lib/i18n/sitemap';
// sections
import { TicketsFleetsView } from '@/sections/universe/tickets/fleets';
// components
import { CustomHelmet } from '@/components/custom';

// ----------------------------------------------------------------------

export const Route = createFileRoute('/_authenticated/(universe)/tickets/fleets')({
	component: withErrorBoundarySentry(TicketsFleetsPage)
});

function TicketsFleetsPage() {
	return (
		<>
			<CustomHelmet title={SiteMap.universe.tickets.fleets.title} description={SiteMap.universe.tickets.fleets.description} />

			<TicketsFleetsView />
		</>
	);
}
