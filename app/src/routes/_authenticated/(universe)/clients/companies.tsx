// tanstack
import { createFileRoute } from '@tanstack/react-router';
// hoc
import { withErrorBoundarySentry } from '@/lib/hoc';
// locales
import { SiteMap } from '@/lib/i18n/sitemap';
// sections
import { ClientsCompaniesView } from '@/sections/universe/clients/companies';
// components
import { CustomHelmet } from '@/components/custom';

// ----------------------------------------------------------------------

export const Route = createFileRoute('/_authenticated/(universe)/clients/companies')({
	component: withErrorBoundarySentry(ClientsCompaniesPage)
});

function ClientsCompaniesPage() {
	return (
		<>
			<CustomHelmet title={SiteMap.universe.clients.companies.title} description={SiteMap.universe.clients.companies.description} />

			<ClientsCompaniesView />
		</>
	);
}
