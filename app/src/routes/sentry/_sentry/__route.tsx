// @ts-noCheck

import { Link, Outlet, createFileRoute } from '@tanstack/react-router'

import * as React from 'react'

export const Route = createFileRoute('/sentry/_sentry')({
	beforeLoad: async () => {
		if (window.env.APP_ENV === 'production') {
			throw redirect({
				to: '/'
			})
		}
	},
	component: () => {
		return (
			<div className='flex h-screen w-full flex-col'>
				<header>
					<nav className='flex h-10 items-center justify-center gap-x-4 bg-free-white'>
						<Link to='/sentry/crash/1' className='text-free-state-info underline'>
							crash page test 1
						</Link>
						<Link to='/sentry/crash/2' className='text-free-state-info underline'>
							crash page test 2
						</Link>
						<Link to='/sentry/crash/3' className='text-free-state-info underline'>
							crash page test 3
						</Link>
						<Link to='/sentry' className='text-free-state-info underline'>
							sentry
						</Link>
					</nav>
				</header>
				<Outlet />
			</div>
		)
	}
})
