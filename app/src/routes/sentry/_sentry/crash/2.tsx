// @ts-noCheck
import { createFileRoute, ErrorComponent } from '@tanstack/react-router'
import { withErrorBoundarySentry } from '@/hoc'

export const Route = createFileRoute('/sentry/_sentry/crash/2')({
	component: withErrorBoundarySentry(SentryCrash2Page, {
		fallback: ({ error, componentStack, eventId }) => {
			return (
				<ErrorComponent
					error={{
						message: `
				error: ${JSON.stringify(error, null, 2)}\n
				componentStack: ${JSON.stringify(componentStack, null, 2)}\n
				eventId: ${JSON.stringify(eventId, null, 2)}\n
				`
					}}
				/>
			)
		}
	})
})

function SentryCrash2Page() {
	const keyC = 'lol_2_crach_is_withErrorBoundary'
	const data = {}
	return (
		<div>
			<title>Sentry crach 2 With Error Boundary</title>

			<main
				style={{
					minHeight: '100vh',
					display: 'flex',
					flexDirection: 'column',
					justifyContent: 'center',
					alignItems: 'center'
				}}
			>
				{data[keyC]._2_crach_is_withErrorBoundary}
			</main>
		</div>
	)
}
