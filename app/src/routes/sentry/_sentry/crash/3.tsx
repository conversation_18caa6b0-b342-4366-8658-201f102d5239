// @ts-noCheck
import { createFileRoute, ErrorComponent } from '@tanstack/react-router'

import { Button } from '@tools/reactore'
import { useState } from 'react'
import { withErrorBoundarySentry } from '@/hoc'

export const Route = createFileRoute('/sentry/_sentry/crash/3')({
	component: withErrorBoundarySentry(SentryCrash3Page, {
		fallback: ({ error, componentStack, eventId }) => {
			return (
				<ErrorComponent
					error={{
						message: `
				error: ${JSON.stringify(error, null, 2)}\n
				componentStack: ${JSON.stringify(componentStack, null, 2)}\n
				eventId: ${JSON.stringify(eventId, null, 2)}\n
				`
					}}
				/>
			)
		}
	})
})

function SentryCrash3Page() {
	const keyC = 'lol_3_button_crach_is_withErrorBoundary'
	const [data, setData] = useState({})
	return (
		<div>
			<title>Sentry crach 3 With Error Boundary</title>

			<main
				style={{
					minHeight: '100vh',
					display: 'flex',
					flexDirection: 'column',
					justifyContent: 'center',
					alignItems: 'center'
				}}
			>
				<p>{data[keyC]?._3_button_crach_is_withErrorBoundary}</p>
				<Button
					onClick={() => {
						setData(null)
					}}
				>
					crash 3
				</Button>
			</main>
		</div>
	)
}
