// others
import packageJson from '../package.json';

// ----------------------------------------------------------------------

export type ConfigValue = {
	appName: string;
	appVersion: string;
	serverUrl: string;
	assetsDir: string;
	isStaticExport: boolean;
};

// ----------------------------------------------------------------------

export const CONFIG: ConfigValue = {
	appName: 'Cosmos',
	appVersion: packageJson.version,
	serverUrl: import.meta.env.VITE_SERVER_URL ?? '',
	assetsDir: import.meta.env.VITE_ASSET_URL ?? '',
	isStaticExport: JSON.parse(import.meta.env.VITE_STATIC_EXPORT ?? 'false')
};

// PROJECT
// ----------------------------------------------------------------------
export const PROJECT_NAME = import.meta.env.VITE_PROJECT_NAME;
export const PROJECT_DEPLOYMENT = import.meta.env.VITE_PROJECT_DEPLOYMENT;
export const PROJECT_BUILD_DATE = import.meta.env.VITE_PROJECT_BUILDER_DATE || new Date().toString();
export const PROJECT_VERSION = import.meta.env.VITE_PROJECT_BUILDER_VERSION || 'UNDEFINED';
export const PROJECT_ENV = (window as any).env.APP_ENV || 'UNDEFINED';
export const SENTRY_STATE = (window as any).env.SENTRY_STATE || 'UNDEFINED';

export const IS_DEV = PROJECT_ENV === 'local';
export const IS_PROD = PROJECT_ENV === 'production';

// API
// ----------------------------------------------------------------------
export const CELL_API = (window as any).env.PUBLIC_CELL_API_URL || import.meta.env.VITE_CELL_API;
export const DELIVER_API = (window as any).env.PUBLIC_DELIVER_API_URL || import.meta.env.VITE_DELIVER_API;
export const MAGNET_API = (window as any).env.PUBLIC_MAGNET_API_URL || import.meta.env.VITE_MAGENT_API;
export const METEOR_API = (window as any).env.PUBLIC_METEOR_API_URL || import.meta.env.VITE_METEOR_API;
export const PAWN_API = (window as any).env.PUBLIC_PAWN_API_URL || import.meta.env.VITE_PAWN_API;
export const PIGGY_API = (window as any).env.PUBLIC_PIGGY_API_URL || import.meta.env.VITE_PIGGY_API;

// ROOT path AFTER LOGIN SUCCESSFUL
export const PATH_AFTER_LOGIN = '/fiber/orders';

export const MAPBOX_API = import.meta.env.VITE_MAPBOX_API;

export const mapBaseSettings = {
	mapboxAccessToken: MAPBOX_API,
	minZoom: 1
};
