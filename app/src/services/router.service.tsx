// tanstack
import { createRouter, ErrorComponent } from '@tanstack/react-router';
// components
import { CustomLoading } from '@/components/custom';
// others
import { routeTree } from '../routeTree.gen';

// ----------------------------------------------------------------------

// Register the router instance for type safety
declare module '@tanstack/react-router' {
	interface Register {
		router: typeof router;
	}
}

export const router = createRouter({
	routeTree,
	defaultPreload: 'intent',
	context: {
		auth: undefined!, // We'll inject this when we render
		queryClient: undefined! // We'll inject this when we render
	},
	defaultPendingComponent: () => (
		<div className='flex w-screen h-screen flex-full-center'>
			<CustomLoading />
		</div>
	),
	defaultErrorComponent: ({ error }) => <ErrorComponent error={error} />,
	// defaultNotFoundComponent: () => <Error404 />,

	// Since we're using React Query, we don't want loader calls to ever be stale
	// This will ensure that the loader is always called when the route is preloaded or visited
	defaultPreloadStaleTime: 0
});
