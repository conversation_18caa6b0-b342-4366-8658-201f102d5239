// tanstack
import { QueryClient, type QueryFunction, type QueryKey } from '@tanstack/react-query';
// utils
import * as Sentry from '@sentry/react';
import { getMinute, StorageService } from '@tools/reactore';
// api
import type { TPawnStaffLoginFetchResponse } from '@/api/types';
import { fetcherFn } from '@/api/fetcher';
import { pawnQueryKeys } from '@/api/queries';
// constants
import { globalEnv } from '@/env';
// types
import type { TApiArg } from '@/types/fetcher.types';
// others
import { mutationDefaults } from './mutation-defaults.service';

// ----------------------------------------------------------------------

export const fiberFetcher = <T>(args: TApiArg): Promise<T> => {
	return fetcherFn<T>({
		...args,
		config: {
			...args.config,
			baseURL: globalEnv.env.PUBLIC_FIBER_API_URL
		}
	});
};
export const cellFetcher = <T>(args: TApiArg): Promise<T> => {
	return fetcherFn<T>({
		...args,
		config: {
			...args.config,
			baseURL: globalEnv.env.PUBLIC_CELL_API_URL
		}
	});
};
export const deliverFetcher = <T>(args: TApiArg): Promise<T> => {
	return fetcherFn<T>({
		...args,
		config: {
			...args.config,
			baseURL: globalEnv.env.PUBLIC_DELIVER_API_URL
		}
	});
};
export const magnetFetcher = <T>(args: TApiArg): Promise<T> => {
	return fetcherFn<T>({
		...args,
		config: {
			...args.config,
			baseURL: globalEnv.env.PUBLIC_MAGNET_API_URL
		}
	});
};
export const pawnFetcher = <T>(args: TApiArg): Promise<T> => {
	return fetcherFn<T>({
		...args,
		config: {
			...args.config,
			baseURL: globalEnv.env.PUBLIC_PAWN_API_URL
		}
	});
};
export const piggyFetcher = <T>(args: TApiArg): Promise<T> => {
	return fetcherFn<T>({
		...args,
		config: {
			...args.config,
			baseURL: globalEnv.env.PUBLIC_PIGGY_API_URL
		}
	});
};

const queryFn: QueryFunction<unknown, QueryKey> = async ({ queryKey }) => {
	const url = queryKey[0] as unknown as any;
	const params = queryKey[1];
	const config = queryKey[2] as Record<any, any>;

	const storage = new StorageService();
	const token = storage.read('userToken');

	return fetcherFn({
		url,
		params,
		config: {
			...config,
			headers: {
				...config.headers,
				Authorization: `Bearer ${token}`
			}
		}
	});
};

const queryClient = new QueryClient({
	defaultOptions: {
		queries: {
			// refetchOnWindowFocus: APP_ENV === 'production', // default: true
			refetchInterval: getMinute(10),
			refetchOnReconnect: 'always',
			refetchOnWindowFocus: 'always',
			// refetchOnMount: true,
			queryFn,
			retry: false,
			staleTime: getMinute(5) // tmp
		}
	}
});

mutationDefaults(queryClient);

queryClient.setMutationDefaults(['refresh_token'], {
	mutationFn: () => {
		const storage = new StorageService('localStorage');

		return pawnFetcher<TPawnStaffLoginFetchResponse>({
			url: 'staff/refresh_token',
			data: [],
			method: 'post',
			config: {
				headers: {
					Authorization: `Bearer ${storage.read('userToken')}`,
					ContentType: 'application/ld+json',
					'Content-Type': 'application/ld+json'
				}
			}
		});
	},
	onSuccess(loginRep) {
		const storage = new StorageService('localStorage');

		storage.addItem('userToken', loginRep.payload?.token);
		queryClient.invalidateQueries({
			queryKey: pawnQueryKeys.pawn_staff._def
		});
	},
	onError() {
		Sentry.setUser(null);
	}
});

export { queryClient };
