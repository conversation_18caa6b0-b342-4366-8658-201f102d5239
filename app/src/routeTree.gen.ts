/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as OfflineImport } from './routes/offline'
import { Route as AuthenticatedImport } from './routes/_authenticated'
import { Route as AuthImport } from './routes/_auth'
import { Route as AuthenticatedIndexImport } from './routes/_authenticated/index'
import { Route as AuthenticatedDashboardImport } from './routes/_authenticated/dashboard'
import { Route as AuthenticatedAdminImport } from './routes/_authenticated/admin'
import { Route as AuthRegisterImport } from './routes/_auth/register'
import { Route as AuthLogoutImport } from './routes/_auth/logout'
import { Route as AuthLoginImport } from './routes/_auth/login'
import { Route as AuthEmailConfirmImport } from './routes/_auth/email-confirm'
import { Route as SentrySentryrouteImport } from './routes/sentry/_sentry/__route'
import { Route as SentrySentryIndexImport } from './routes/sentry/_sentry/index'
import { Route as AuthpasswordSetPasswordImport } from './routes/_auth/(password)/set-password'
import { Route as AuthpasswordResetPasswordImport } from './routes/_auth/(password)/reset-password'
import { Route as AuthenticateduniverseFiberIndexImport } from './routes/_authenticated/(universe)/fiber/index'
import { Route as SentrySentryCrash3Import } from './routes/sentry/_sentry/crash/3'
import { Route as SentrySentryCrash2Import } from './routes/sentry/_sentry/crash/2'
import { Route as SentrySentryCrash1Import } from './routes/sentry/_sentry/crash/1'
import { Route as AuthenticateduniverseTicketsMobileImport } from './routes/_authenticated/(universe)/tickets/mobile'
import { Route as AuthenticateduniverseTicketsFleetsImport } from './routes/_authenticated/(universe)/tickets/fleets'
import { Route as AuthenticateduniverseTicketsIdImport } from './routes/_authenticated/(universe)/tickets/$id'
import { Route as AuthenticateduniverseMobileTerminalsImport } from './routes/_authenticated/(universe)/mobile/terminals'
import { Route as AuthenticateduniverseMobileSimImport } from './routes/_authenticated/(universe)/mobile/sim'
import { Route as AuthenticateduniverseMobileProImport } from './routes/_authenticated/(universe)/mobile/pro'
import { Route as AuthenticateduniverseMobilePortabilityImport } from './routes/_authenticated/(universe)/mobile/portability'
import { Route as AuthenticateduniverseMobileFleetsImport } from './routes/_authenticated/(universe)/mobile/fleets'
import { Route as AuthenticateduniverseFiberParkManagementImport } from './routes/_authenticated/(universe)/fiber/park-management'
import { Route as AuthenticateduniverseFiberInterventionsImport } from './routes/_authenticated/(universe)/fiber/interventions'
import { Route as AuthenticateduniverseFiberEligibilityImport } from './routes/_authenticated/(universe)/fiber/eligibility'
import { Route as AuthenticateduniverseFiberBlocksImport } from './routes/_authenticated/(universe)/fiber/blocks'
import { Route as AuthenticateduniverseClientsQuotesImport } from './routes/_authenticated/(universe)/clients/quotes'
import { Route as AuthenticateduniverseClientsOrdersImport } from './routes/_authenticated/(universe)/clients/orders'
import { Route as AuthenticateduniverseClientsInvoicesImport } from './routes/_authenticated/(universe)/clients/invoices'
import { Route as AuthenticateduniverseClientsCompaniesImport } from './routes/_authenticated/(universe)/clients/companies'
import { Route as AuthenticateduniverseTicketsFiberIndexImport } from './routes/_authenticated/(universe)/tickets/fiber/index'
import { Route as AuthenticateduniverseFiberOrdersIndexImport } from './routes/_authenticated/(universe)/fiber/orders/index'
import { Route as AuthenticateduniverseTicketsFiberIdImport } from './routes/_authenticated/(universe)/tickets/fiber/$id'
import { Route as AuthenticateduniverseFiberOrdersIdImport } from './routes/_authenticated/(universe)/fiber/orders/$id'
import { Route as AuthenticateduniverseTechFiberOrdersIdImport } from './routes/_authenticated/(universe)/tech/fiber/orders/$id'

// Create Virtual Routes

const SentryImport = createFileRoute('/sentry')()
const errors503LazyImport = createFileRoute('/(errors)/503')()
const errors500LazyImport = createFileRoute('/(errors)/500')()
const errors404LazyImport = createFileRoute('/(errors)/404')()
const errors403LazyImport = createFileRoute('/(errors)/403')()
const errors401LazyImport = createFileRoute('/(errors)/401')()

// Create/Update Routes

const SentryRoute = SentryImport.update({
  id: '/sentry',
  path: '/sentry',
  getParentRoute: () => rootRoute,
} as any)

const OfflineRoute = OfflineImport.update({
  id: '/offline',
  path: '/offline',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedRoute = AuthenticatedImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRoute,
} as any)

const AuthRoute = AuthImport.update({
  id: '/_auth',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedIndexRoute = AuthenticatedIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRoute,
} as any)

const errors503LazyRoute = errors503LazyImport
  .update({
    id: '/(errors)/503',
    path: '/503',
    getParentRoute: () => rootRoute,
  } as any)
  .lazy(() => import('./routes/(errors)/503.lazy').then((d) => d.Route))

const errors500LazyRoute = errors500LazyImport
  .update({
    id: '/(errors)/500',
    path: '/500',
    getParentRoute: () => rootRoute,
  } as any)
  .lazy(() => import('./routes/(errors)/500.lazy').then((d) => d.Route))

const errors404LazyRoute = errors404LazyImport
  .update({
    id: '/(errors)/404',
    path: '/404',
    getParentRoute: () => rootRoute,
  } as any)
  .lazy(() => import('./routes/(errors)/404.lazy').then((d) => d.Route))

const errors403LazyRoute = errors403LazyImport
  .update({
    id: '/(errors)/403',
    path: '/403',
    getParentRoute: () => rootRoute,
  } as any)
  .lazy(() => import('./routes/(errors)/403.lazy').then((d) => d.Route))

const errors401LazyRoute = errors401LazyImport
  .update({
    id: '/(errors)/401',
    path: '/401',
    getParentRoute: () => rootRoute,
  } as any)
  .lazy(() => import('./routes/(errors)/401.lazy').then((d) => d.Route))

const AuthenticatedDashboardRoute = AuthenticatedDashboardImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => AuthenticatedRoute,
} as any)

const AuthenticatedAdminRoute = AuthenticatedAdminImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => AuthenticatedRoute,
} as any)

const AuthRegisterRoute = AuthRegisterImport.update({
  id: '/register',
  path: '/register',
  getParentRoute: () => AuthRoute,
} as any)

const AuthLogoutRoute = AuthLogoutImport.update({
  id: '/logout',
  path: '/logout',
  getParentRoute: () => AuthRoute,
} as any)

const AuthLoginRoute = AuthLoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => AuthRoute,
} as any)

const AuthEmailConfirmRoute = AuthEmailConfirmImport.update({
  id: '/email-confirm',
  path: '/email-confirm',
  getParentRoute: () => AuthRoute,
} as any)

const SentrySentryrouteRoute = SentrySentryrouteImport.update({
  id: '/_sentry',
  getParentRoute: () => SentryRoute,
} as any)

const SentrySentryIndexRoute = SentrySentryIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => SentrySentryrouteRoute,
} as any)

const AuthpasswordSetPasswordRoute = AuthpasswordSetPasswordImport.update({
  id: '/(password)/set-password',
  path: '/set-password',
  getParentRoute: () => AuthRoute,
} as any)

const AuthpasswordResetPasswordRoute = AuthpasswordResetPasswordImport.update({
  id: '/(password)/reset-password',
  path: '/reset-password',
  getParentRoute: () => AuthRoute,
} as any)

const AuthenticateduniverseFiberIndexRoute =
  AuthenticateduniverseFiberIndexImport.update({
    id: '/(universe)/fiber/',
    path: '/fiber/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const SentrySentryCrash3Route = SentrySentryCrash3Import.update({
  id: '/crash/3',
  path: '/crash/3',
  getParentRoute: () => SentrySentryrouteRoute,
} as any)

const SentrySentryCrash2Route = SentrySentryCrash2Import.update({
  id: '/crash/2',
  path: '/crash/2',
  getParentRoute: () => SentrySentryrouteRoute,
} as any)

const SentrySentryCrash1Route = SentrySentryCrash1Import.update({
  id: '/crash/1',
  path: '/crash/1',
  getParentRoute: () => SentrySentryrouteRoute,
} as any)

const AuthenticateduniverseTicketsMobileRoute =
  AuthenticateduniverseTicketsMobileImport.update({
    id: '/(universe)/tickets/mobile',
    path: '/tickets/mobile',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseTicketsFleetsRoute =
  AuthenticateduniverseTicketsFleetsImport.update({
    id: '/(universe)/tickets/fleets',
    path: '/tickets/fleets',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseTicketsIdRoute =
  AuthenticateduniverseTicketsIdImport.update({
    id: '/(universe)/tickets/$id',
    path: '/tickets/$id',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseMobileTerminalsRoute =
  AuthenticateduniverseMobileTerminalsImport.update({
    id: '/(universe)/mobile/terminals',
    path: '/mobile/terminals',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseMobileSimRoute =
  AuthenticateduniverseMobileSimImport.update({
    id: '/(universe)/mobile/sim',
    path: '/mobile/sim',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseMobileProRoute =
  AuthenticateduniverseMobileProImport.update({
    id: '/(universe)/mobile/pro',
    path: '/mobile/pro',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseMobilePortabilityRoute =
  AuthenticateduniverseMobilePortabilityImport.update({
    id: '/(universe)/mobile/portability',
    path: '/mobile/portability',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseMobileFleetsRoute =
  AuthenticateduniverseMobileFleetsImport.update({
    id: '/(universe)/mobile/fleets',
    path: '/mobile/fleets',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseFiberParkManagementRoute =
  AuthenticateduniverseFiberParkManagementImport.update({
    id: '/(universe)/fiber/park-management',
    path: '/fiber/park-management',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseFiberInterventionsRoute =
  AuthenticateduniverseFiberInterventionsImport.update({
    id: '/(universe)/fiber/interventions',
    path: '/fiber/interventions',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseFiberEligibilityRoute =
  AuthenticateduniverseFiberEligibilityImport.update({
    id: '/(universe)/fiber/eligibility',
    path: '/fiber/eligibility',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseFiberBlocksRoute =
  AuthenticateduniverseFiberBlocksImport.update({
    id: '/(universe)/fiber/blocks',
    path: '/fiber/blocks',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseClientsQuotesRoute =
  AuthenticateduniverseClientsQuotesImport.update({
    id: '/(universe)/clients/quotes',
    path: '/clients/quotes',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseClientsOrdersRoute =
  AuthenticateduniverseClientsOrdersImport.update({
    id: '/(universe)/clients/orders',
    path: '/clients/orders',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseClientsInvoicesRoute =
  AuthenticateduniverseClientsInvoicesImport.update({
    id: '/(universe)/clients/invoices',
    path: '/clients/invoices',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseClientsCompaniesRoute =
  AuthenticateduniverseClientsCompaniesImport.update({
    id: '/(universe)/clients/companies',
    path: '/clients/companies',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseTicketsFiberIndexRoute =
  AuthenticateduniverseTicketsFiberIndexImport.update({
    id: '/(universe)/tickets/fiber/',
    path: '/tickets/fiber/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseFiberOrdersIndexRoute =
  AuthenticateduniverseFiberOrdersIndexImport.update({
    id: '/(universe)/fiber/orders/',
    path: '/fiber/orders/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseTicketsFiberIdRoute =
  AuthenticateduniverseTicketsFiberIdImport.update({
    id: '/(universe)/tickets/fiber/$id',
    path: '/tickets/fiber/$id',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseFiberOrdersIdRoute =
  AuthenticateduniverseFiberOrdersIdImport.update({
    id: '/(universe)/fiber/orders/$id',
    path: '/fiber/orders/$id',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticateduniverseTechFiberOrdersIdRoute =
  AuthenticateduniverseTechFiberOrdersIdImport.update({
    id: '/(universe)/tech/fiber/orders/$id',
    path: '/tech/fiber/orders/$id',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_auth': {
      id: '/_auth'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthImport
      parentRoute: typeof rootRoute
    }
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedImport
      parentRoute: typeof rootRoute
    }
    '/offline': {
      id: '/offline'
      path: '/offline'
      fullPath: '/offline'
      preLoaderRoute: typeof OfflineImport
      parentRoute: typeof rootRoute
    }
    '/sentry': {
      id: '/sentry'
      path: '/sentry'
      fullPath: '/sentry'
      preLoaderRoute: typeof SentryImport
      parentRoute: typeof rootRoute
    }
    '/sentry/_sentry': {
      id: '/sentry/_sentry'
      path: '/sentry'
      fullPath: '/sentry'
      preLoaderRoute: typeof SentrySentryrouteImport
      parentRoute: typeof SentryRoute
    }
    '/_auth/email-confirm': {
      id: '/_auth/email-confirm'
      path: '/email-confirm'
      fullPath: '/email-confirm'
      preLoaderRoute: typeof AuthEmailConfirmImport
      parentRoute: typeof AuthImport
    }
    '/_auth/login': {
      id: '/_auth/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof AuthLoginImport
      parentRoute: typeof AuthImport
    }
    '/_auth/logout': {
      id: '/_auth/logout'
      path: '/logout'
      fullPath: '/logout'
      preLoaderRoute: typeof AuthLogoutImport
      parentRoute: typeof AuthImport
    }
    '/_auth/register': {
      id: '/_auth/register'
      path: '/register'
      fullPath: '/register'
      preLoaderRoute: typeof AuthRegisterImport
      parentRoute: typeof AuthImport
    }
    '/_authenticated/admin': {
      id: '/_authenticated/admin'
      path: '/admin'
      fullPath: '/admin'
      preLoaderRoute: typeof AuthenticatedAdminImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/dashboard': {
      id: '/_authenticated/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof AuthenticatedDashboardImport
      parentRoute: typeof AuthenticatedImport
    }
    '/(errors)/401': {
      id: '/(errors)/401'
      path: '/401'
      fullPath: '/401'
      preLoaderRoute: typeof errors401LazyImport
      parentRoute: typeof rootRoute
    }
    '/(errors)/403': {
      id: '/(errors)/403'
      path: '/403'
      fullPath: '/403'
      preLoaderRoute: typeof errors403LazyImport
      parentRoute: typeof rootRoute
    }
    '/(errors)/404': {
      id: '/(errors)/404'
      path: '/404'
      fullPath: '/404'
      preLoaderRoute: typeof errors404LazyImport
      parentRoute: typeof rootRoute
    }
    '/(errors)/500': {
      id: '/(errors)/500'
      path: '/500'
      fullPath: '/500'
      preLoaderRoute: typeof errors500LazyImport
      parentRoute: typeof rootRoute
    }
    '/(errors)/503': {
      id: '/(errors)/503'
      path: '/503'
      fullPath: '/503'
      preLoaderRoute: typeof errors503LazyImport
      parentRoute: typeof rootRoute
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_auth/(password)/reset-password': {
      id: '/_auth/(password)/reset-password'
      path: '/reset-password'
      fullPath: '/reset-password'
      preLoaderRoute: typeof AuthpasswordResetPasswordImport
      parentRoute: typeof AuthImport
    }
    '/_auth/(password)/set-password': {
      id: '/_auth/(password)/set-password'
      path: '/set-password'
      fullPath: '/set-password'
      preLoaderRoute: typeof AuthpasswordSetPasswordImport
      parentRoute: typeof AuthImport
    }
    '/sentry/_sentry/': {
      id: '/sentry/_sentry/'
      path: '/'
      fullPath: '/sentry/'
      preLoaderRoute: typeof SentrySentryIndexImport
      parentRoute: typeof SentrySentryrouteImport
    }
    '/_authenticated/(universe)/clients/companies': {
      id: '/_authenticated/(universe)/clients/companies'
      path: '/clients/companies'
      fullPath: '/clients/companies'
      preLoaderRoute: typeof AuthenticateduniverseClientsCompaniesImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/clients/invoices': {
      id: '/_authenticated/(universe)/clients/invoices'
      path: '/clients/invoices'
      fullPath: '/clients/invoices'
      preLoaderRoute: typeof AuthenticateduniverseClientsInvoicesImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/clients/orders': {
      id: '/_authenticated/(universe)/clients/orders'
      path: '/clients/orders'
      fullPath: '/clients/orders'
      preLoaderRoute: typeof AuthenticateduniverseClientsOrdersImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/clients/quotes': {
      id: '/_authenticated/(universe)/clients/quotes'
      path: '/clients/quotes'
      fullPath: '/clients/quotes'
      preLoaderRoute: typeof AuthenticateduniverseClientsQuotesImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/fiber/blocks': {
      id: '/_authenticated/(universe)/fiber/blocks'
      path: '/fiber/blocks'
      fullPath: '/fiber/blocks'
      preLoaderRoute: typeof AuthenticateduniverseFiberBlocksImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/fiber/eligibility': {
      id: '/_authenticated/(universe)/fiber/eligibility'
      path: '/fiber/eligibility'
      fullPath: '/fiber/eligibility'
      preLoaderRoute: typeof AuthenticateduniverseFiberEligibilityImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/fiber/interventions': {
      id: '/_authenticated/(universe)/fiber/interventions'
      path: '/fiber/interventions'
      fullPath: '/fiber/interventions'
      preLoaderRoute: typeof AuthenticateduniverseFiberInterventionsImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/fiber/park-management': {
      id: '/_authenticated/(universe)/fiber/park-management'
      path: '/fiber/park-management'
      fullPath: '/fiber/park-management'
      preLoaderRoute: typeof AuthenticateduniverseFiberParkManagementImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/mobile/fleets': {
      id: '/_authenticated/(universe)/mobile/fleets'
      path: '/mobile/fleets'
      fullPath: '/mobile/fleets'
      preLoaderRoute: typeof AuthenticateduniverseMobileFleetsImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/mobile/portability': {
      id: '/_authenticated/(universe)/mobile/portability'
      path: '/mobile/portability'
      fullPath: '/mobile/portability'
      preLoaderRoute: typeof AuthenticateduniverseMobilePortabilityImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/mobile/pro': {
      id: '/_authenticated/(universe)/mobile/pro'
      path: '/mobile/pro'
      fullPath: '/mobile/pro'
      preLoaderRoute: typeof AuthenticateduniverseMobileProImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/mobile/sim': {
      id: '/_authenticated/(universe)/mobile/sim'
      path: '/mobile/sim'
      fullPath: '/mobile/sim'
      preLoaderRoute: typeof AuthenticateduniverseMobileSimImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/mobile/terminals': {
      id: '/_authenticated/(universe)/mobile/terminals'
      path: '/mobile/terminals'
      fullPath: '/mobile/terminals'
      preLoaderRoute: typeof AuthenticateduniverseMobileTerminalsImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/tickets/$id': {
      id: '/_authenticated/(universe)/tickets/$id'
      path: '/tickets/$id'
      fullPath: '/tickets/$id'
      preLoaderRoute: typeof AuthenticateduniverseTicketsIdImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/tickets/fleets': {
      id: '/_authenticated/(universe)/tickets/fleets'
      path: '/tickets/fleets'
      fullPath: '/tickets/fleets'
      preLoaderRoute: typeof AuthenticateduniverseTicketsFleetsImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/tickets/mobile': {
      id: '/_authenticated/(universe)/tickets/mobile'
      path: '/tickets/mobile'
      fullPath: '/tickets/mobile'
      preLoaderRoute: typeof AuthenticateduniverseTicketsMobileImport
      parentRoute: typeof AuthenticatedImport
    }
    '/sentry/_sentry/crash/1': {
      id: '/sentry/_sentry/crash/1'
      path: '/crash/1'
      fullPath: '/sentry/crash/1'
      preLoaderRoute: typeof SentrySentryCrash1Import
      parentRoute: typeof SentrySentryrouteImport
    }
    '/sentry/_sentry/crash/2': {
      id: '/sentry/_sentry/crash/2'
      path: '/crash/2'
      fullPath: '/sentry/crash/2'
      preLoaderRoute: typeof SentrySentryCrash2Import
      parentRoute: typeof SentrySentryrouteImport
    }
    '/sentry/_sentry/crash/3': {
      id: '/sentry/_sentry/crash/3'
      path: '/crash/3'
      fullPath: '/sentry/crash/3'
      preLoaderRoute: typeof SentrySentryCrash3Import
      parentRoute: typeof SentrySentryrouteImport
    }
    '/_authenticated/(universe)/fiber/': {
      id: '/_authenticated/(universe)/fiber/'
      path: '/fiber'
      fullPath: '/fiber'
      preLoaderRoute: typeof AuthenticateduniverseFiberIndexImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/fiber/orders/$id': {
      id: '/_authenticated/(universe)/fiber/orders/$id'
      path: '/fiber/orders/$id'
      fullPath: '/fiber/orders/$id'
      preLoaderRoute: typeof AuthenticateduniverseFiberOrdersIdImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/tickets/fiber/$id': {
      id: '/_authenticated/(universe)/tickets/fiber/$id'
      path: '/tickets/fiber/$id'
      fullPath: '/tickets/fiber/$id'
      preLoaderRoute: typeof AuthenticateduniverseTicketsFiberIdImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/fiber/orders/': {
      id: '/_authenticated/(universe)/fiber/orders/'
      path: '/fiber/orders'
      fullPath: '/fiber/orders'
      preLoaderRoute: typeof AuthenticateduniverseFiberOrdersIndexImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/tickets/fiber/': {
      id: '/_authenticated/(universe)/tickets/fiber/'
      path: '/tickets/fiber'
      fullPath: '/tickets/fiber'
      preLoaderRoute: typeof AuthenticateduniverseTicketsFiberIndexImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/(universe)/tech/fiber/orders/$id': {
      id: '/_authenticated/(universe)/tech/fiber/orders/$id'
      path: '/tech/fiber/orders/$id'
      fullPath: '/tech/fiber/orders/$id'
      preLoaderRoute: typeof AuthenticateduniverseTechFiberOrdersIdImport
      parentRoute: typeof AuthenticatedImport
    }
  }
}

// Create and export the route tree

interface AuthRouteChildren {
  AuthEmailConfirmRoute: typeof AuthEmailConfirmRoute
  AuthLoginRoute: typeof AuthLoginRoute
  AuthLogoutRoute: typeof AuthLogoutRoute
  AuthRegisterRoute: typeof AuthRegisterRoute
  AuthpasswordResetPasswordRoute: typeof AuthpasswordResetPasswordRoute
  AuthpasswordSetPasswordRoute: typeof AuthpasswordSetPasswordRoute
}

const AuthRouteChildren: AuthRouteChildren = {
  AuthEmailConfirmRoute: AuthEmailConfirmRoute,
  AuthLoginRoute: AuthLoginRoute,
  AuthLogoutRoute: AuthLogoutRoute,
  AuthRegisterRoute: AuthRegisterRoute,
  AuthpasswordResetPasswordRoute: AuthpasswordResetPasswordRoute,
  AuthpasswordSetPasswordRoute: AuthpasswordSetPasswordRoute,
}

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren)

interface AuthenticatedRouteChildren {
  AuthenticatedAdminRoute: typeof AuthenticatedAdminRoute
  AuthenticatedDashboardRoute: typeof AuthenticatedDashboardRoute
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
  AuthenticateduniverseClientsCompaniesRoute: typeof AuthenticateduniverseClientsCompaniesRoute
  AuthenticateduniverseClientsInvoicesRoute: typeof AuthenticateduniverseClientsInvoicesRoute
  AuthenticateduniverseClientsOrdersRoute: typeof AuthenticateduniverseClientsOrdersRoute
  AuthenticateduniverseClientsQuotesRoute: typeof AuthenticateduniverseClientsQuotesRoute
  AuthenticateduniverseFiberBlocksRoute: typeof AuthenticateduniverseFiberBlocksRoute
  AuthenticateduniverseFiberEligibilityRoute: typeof AuthenticateduniverseFiberEligibilityRoute
  AuthenticateduniverseFiberInterventionsRoute: typeof AuthenticateduniverseFiberInterventionsRoute
  AuthenticateduniverseFiberParkManagementRoute: typeof AuthenticateduniverseFiberParkManagementRoute
  AuthenticateduniverseMobileFleetsRoute: typeof AuthenticateduniverseMobileFleetsRoute
  AuthenticateduniverseMobilePortabilityRoute: typeof AuthenticateduniverseMobilePortabilityRoute
  AuthenticateduniverseMobileProRoute: typeof AuthenticateduniverseMobileProRoute
  AuthenticateduniverseMobileSimRoute: typeof AuthenticateduniverseMobileSimRoute
  AuthenticateduniverseMobileTerminalsRoute: typeof AuthenticateduniverseMobileTerminalsRoute
  AuthenticateduniverseTicketsIdRoute: typeof AuthenticateduniverseTicketsIdRoute
  AuthenticateduniverseTicketsFleetsRoute: typeof AuthenticateduniverseTicketsFleetsRoute
  AuthenticateduniverseTicketsMobileRoute: typeof AuthenticateduniverseTicketsMobileRoute
  AuthenticateduniverseFiberIndexRoute: typeof AuthenticateduniverseFiberIndexRoute
  AuthenticateduniverseFiberOrdersIdRoute: typeof AuthenticateduniverseFiberOrdersIdRoute
  AuthenticateduniverseTicketsFiberIdRoute: typeof AuthenticateduniverseTicketsFiberIdRoute
  AuthenticateduniverseFiberOrdersIndexRoute: typeof AuthenticateduniverseFiberOrdersIndexRoute
  AuthenticateduniverseTicketsFiberIndexRoute: typeof AuthenticateduniverseTicketsFiberIndexRoute
  AuthenticateduniverseTechFiberOrdersIdRoute: typeof AuthenticateduniverseTechFiberOrdersIdRoute
}

const AuthenticatedRouteChildren: AuthenticatedRouteChildren = {
  AuthenticatedAdminRoute: AuthenticatedAdminRoute,
  AuthenticatedDashboardRoute: AuthenticatedDashboardRoute,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
  AuthenticateduniverseClientsCompaniesRoute:
    AuthenticateduniverseClientsCompaniesRoute,
  AuthenticateduniverseClientsInvoicesRoute:
    AuthenticateduniverseClientsInvoicesRoute,
  AuthenticateduniverseClientsOrdersRoute:
    AuthenticateduniverseClientsOrdersRoute,
  AuthenticateduniverseClientsQuotesRoute:
    AuthenticateduniverseClientsQuotesRoute,
  AuthenticateduniverseFiberBlocksRoute: AuthenticateduniverseFiberBlocksRoute,
  AuthenticateduniverseFiberEligibilityRoute:
    AuthenticateduniverseFiberEligibilityRoute,
  AuthenticateduniverseFiberInterventionsRoute:
    AuthenticateduniverseFiberInterventionsRoute,
  AuthenticateduniverseFiberParkManagementRoute:
    AuthenticateduniverseFiberParkManagementRoute,
  AuthenticateduniverseMobileFleetsRoute:
    AuthenticateduniverseMobileFleetsRoute,
  AuthenticateduniverseMobilePortabilityRoute:
    AuthenticateduniverseMobilePortabilityRoute,
  AuthenticateduniverseMobileProRoute: AuthenticateduniverseMobileProRoute,
  AuthenticateduniverseMobileSimRoute: AuthenticateduniverseMobileSimRoute,
  AuthenticateduniverseMobileTerminalsRoute:
    AuthenticateduniverseMobileTerminalsRoute,
  AuthenticateduniverseTicketsIdRoute: AuthenticateduniverseTicketsIdRoute,
  AuthenticateduniverseTicketsFleetsRoute:
    AuthenticateduniverseTicketsFleetsRoute,
  AuthenticateduniverseTicketsMobileRoute:
    AuthenticateduniverseTicketsMobileRoute,
  AuthenticateduniverseFiberIndexRoute: AuthenticateduniverseFiberIndexRoute,
  AuthenticateduniverseFiberOrdersIdRoute:
    AuthenticateduniverseFiberOrdersIdRoute,
  AuthenticateduniverseTicketsFiberIdRoute:
    AuthenticateduniverseTicketsFiberIdRoute,
  AuthenticateduniverseFiberOrdersIndexRoute:
    AuthenticateduniverseFiberOrdersIndexRoute,
  AuthenticateduniverseTicketsFiberIndexRoute:
    AuthenticateduniverseTicketsFiberIndexRoute,
  AuthenticateduniverseTechFiberOrdersIdRoute:
    AuthenticateduniverseTechFiberOrdersIdRoute,
}

const AuthenticatedRouteWithChildren = AuthenticatedRoute._addFileChildren(
  AuthenticatedRouteChildren,
)

interface SentrySentryrouteRouteChildren {
  SentrySentryIndexRoute: typeof SentrySentryIndexRoute
  SentrySentryCrash1Route: typeof SentrySentryCrash1Route
  SentrySentryCrash2Route: typeof SentrySentryCrash2Route
  SentrySentryCrash3Route: typeof SentrySentryCrash3Route
}

const SentrySentryrouteRouteChildren: SentrySentryrouteRouteChildren = {
  SentrySentryIndexRoute: SentrySentryIndexRoute,
  SentrySentryCrash1Route: SentrySentryCrash1Route,
  SentrySentryCrash2Route: SentrySentryCrash2Route,
  SentrySentryCrash3Route: SentrySentryCrash3Route,
}

const SentrySentryrouteRouteWithChildren =
  SentrySentryrouteRoute._addFileChildren(SentrySentryrouteRouteChildren)

interface SentryRouteChildren {
  SentrySentryrouteRoute: typeof SentrySentryrouteRouteWithChildren
}

const SentryRouteChildren: SentryRouteChildren = {
  SentrySentryrouteRoute: SentrySentryrouteRouteWithChildren,
}

const SentryRouteWithChildren =
  SentryRoute._addFileChildren(SentryRouteChildren)

export interface FileRoutesByFullPath {
  '': typeof AuthenticatedRouteWithChildren
  '/offline': typeof OfflineRoute
  '/sentry': typeof SentrySentryrouteRouteWithChildren
  '/email-confirm': typeof AuthEmailConfirmRoute
  '/login': typeof AuthLoginRoute
  '/logout': typeof AuthLogoutRoute
  '/register': typeof AuthRegisterRoute
  '/admin': typeof AuthenticatedAdminRoute
  '/dashboard': typeof AuthenticatedDashboardRoute
  '/401': typeof errors401LazyRoute
  '/403': typeof errors403LazyRoute
  '/404': typeof errors404LazyRoute
  '/500': typeof errors500LazyRoute
  '/503': typeof errors503LazyRoute
  '/': typeof AuthenticatedIndexRoute
  '/reset-password': typeof AuthpasswordResetPasswordRoute
  '/set-password': typeof AuthpasswordSetPasswordRoute
  '/sentry/': typeof SentrySentryIndexRoute
  '/clients/companies': typeof AuthenticateduniverseClientsCompaniesRoute
  '/clients/invoices': typeof AuthenticateduniverseClientsInvoicesRoute
  '/clients/orders': typeof AuthenticateduniverseClientsOrdersRoute
  '/clients/quotes': typeof AuthenticateduniverseClientsQuotesRoute
  '/fiber/blocks': typeof AuthenticateduniverseFiberBlocksRoute
  '/fiber/eligibility': typeof AuthenticateduniverseFiberEligibilityRoute
  '/fiber/interventions': typeof AuthenticateduniverseFiberInterventionsRoute
  '/fiber/park-management': typeof AuthenticateduniverseFiberParkManagementRoute
  '/mobile/fleets': typeof AuthenticateduniverseMobileFleetsRoute
  '/mobile/portability': typeof AuthenticateduniverseMobilePortabilityRoute
  '/mobile/pro': typeof AuthenticateduniverseMobileProRoute
  '/mobile/sim': typeof AuthenticateduniverseMobileSimRoute
  '/mobile/terminals': typeof AuthenticateduniverseMobileTerminalsRoute
  '/tickets/$id': typeof AuthenticateduniverseTicketsIdRoute
  '/tickets/fleets': typeof AuthenticateduniverseTicketsFleetsRoute
  '/tickets/mobile': typeof AuthenticateduniverseTicketsMobileRoute
  '/sentry/crash/1': typeof SentrySentryCrash1Route
  '/sentry/crash/2': typeof SentrySentryCrash2Route
  '/sentry/crash/3': typeof SentrySentryCrash3Route
  '/fiber': typeof AuthenticateduniverseFiberIndexRoute
  '/fiber/orders/$id': typeof AuthenticateduniverseFiberOrdersIdRoute
  '/tickets/fiber/$id': typeof AuthenticateduniverseTicketsFiberIdRoute
  '/fiber/orders': typeof AuthenticateduniverseFiberOrdersIndexRoute
  '/tickets/fiber': typeof AuthenticateduniverseTicketsFiberIndexRoute
  '/tech/fiber/orders/$id': typeof AuthenticateduniverseTechFiberOrdersIdRoute
}

export interface FileRoutesByTo {
  '': typeof AuthRouteWithChildren
  '/offline': typeof OfflineRoute
  '/sentry': typeof SentrySentryIndexRoute
  '/email-confirm': typeof AuthEmailConfirmRoute
  '/login': typeof AuthLoginRoute
  '/logout': typeof AuthLogoutRoute
  '/register': typeof AuthRegisterRoute
  '/admin': typeof AuthenticatedAdminRoute
  '/dashboard': typeof AuthenticatedDashboardRoute
  '/401': typeof errors401LazyRoute
  '/403': typeof errors403LazyRoute
  '/404': typeof errors404LazyRoute
  '/500': typeof errors500LazyRoute
  '/503': typeof errors503LazyRoute
  '/': typeof AuthenticatedIndexRoute
  '/reset-password': typeof AuthpasswordResetPasswordRoute
  '/set-password': typeof AuthpasswordSetPasswordRoute
  '/clients/companies': typeof AuthenticateduniverseClientsCompaniesRoute
  '/clients/invoices': typeof AuthenticateduniverseClientsInvoicesRoute
  '/clients/orders': typeof AuthenticateduniverseClientsOrdersRoute
  '/clients/quotes': typeof AuthenticateduniverseClientsQuotesRoute
  '/fiber/blocks': typeof AuthenticateduniverseFiberBlocksRoute
  '/fiber/eligibility': typeof AuthenticateduniverseFiberEligibilityRoute
  '/fiber/interventions': typeof AuthenticateduniverseFiberInterventionsRoute
  '/fiber/park-management': typeof AuthenticateduniverseFiberParkManagementRoute
  '/mobile/fleets': typeof AuthenticateduniverseMobileFleetsRoute
  '/mobile/portability': typeof AuthenticateduniverseMobilePortabilityRoute
  '/mobile/pro': typeof AuthenticateduniverseMobileProRoute
  '/mobile/sim': typeof AuthenticateduniverseMobileSimRoute
  '/mobile/terminals': typeof AuthenticateduniverseMobileTerminalsRoute
  '/tickets/$id': typeof AuthenticateduniverseTicketsIdRoute
  '/tickets/fleets': typeof AuthenticateduniverseTicketsFleetsRoute
  '/tickets/mobile': typeof AuthenticateduniverseTicketsMobileRoute
  '/sentry/crash/1': typeof SentrySentryCrash1Route
  '/sentry/crash/2': typeof SentrySentryCrash2Route
  '/sentry/crash/3': typeof SentrySentryCrash3Route
  '/fiber': typeof AuthenticateduniverseFiberIndexRoute
  '/fiber/orders/$id': typeof AuthenticateduniverseFiberOrdersIdRoute
  '/tickets/fiber/$id': typeof AuthenticateduniverseTicketsFiberIdRoute
  '/fiber/orders': typeof AuthenticateduniverseFiberOrdersIndexRoute
  '/tickets/fiber': typeof AuthenticateduniverseTicketsFiberIndexRoute
  '/tech/fiber/orders/$id': typeof AuthenticateduniverseTechFiberOrdersIdRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_auth': typeof AuthRouteWithChildren
  '/_authenticated': typeof AuthenticatedRouteWithChildren
  '/offline': typeof OfflineRoute
  '/sentry': typeof SentryRouteWithChildren
  '/sentry/_sentry': typeof SentrySentryrouteRouteWithChildren
  '/_auth/email-confirm': typeof AuthEmailConfirmRoute
  '/_auth/login': typeof AuthLoginRoute
  '/_auth/logout': typeof AuthLogoutRoute
  '/_auth/register': typeof AuthRegisterRoute
  '/_authenticated/admin': typeof AuthenticatedAdminRoute
  '/_authenticated/dashboard': typeof AuthenticatedDashboardRoute
  '/(errors)/401': typeof errors401LazyRoute
  '/(errors)/403': typeof errors403LazyRoute
  '/(errors)/404': typeof errors404LazyRoute
  '/(errors)/500': typeof errors500LazyRoute
  '/(errors)/503': typeof errors503LazyRoute
  '/_authenticated/': typeof AuthenticatedIndexRoute
  '/_auth/(password)/reset-password': typeof AuthpasswordResetPasswordRoute
  '/_auth/(password)/set-password': typeof AuthpasswordSetPasswordRoute
  '/sentry/_sentry/': typeof SentrySentryIndexRoute
  '/_authenticated/(universe)/clients/companies': typeof AuthenticateduniverseClientsCompaniesRoute
  '/_authenticated/(universe)/clients/invoices': typeof AuthenticateduniverseClientsInvoicesRoute
  '/_authenticated/(universe)/clients/orders': typeof AuthenticateduniverseClientsOrdersRoute
  '/_authenticated/(universe)/clients/quotes': typeof AuthenticateduniverseClientsQuotesRoute
  '/_authenticated/(universe)/fiber/blocks': typeof AuthenticateduniverseFiberBlocksRoute
  '/_authenticated/(universe)/fiber/eligibility': typeof AuthenticateduniverseFiberEligibilityRoute
  '/_authenticated/(universe)/fiber/interventions': typeof AuthenticateduniverseFiberInterventionsRoute
  '/_authenticated/(universe)/fiber/park-management': typeof AuthenticateduniverseFiberParkManagementRoute
  '/_authenticated/(universe)/mobile/fleets': typeof AuthenticateduniverseMobileFleetsRoute
  '/_authenticated/(universe)/mobile/portability': typeof AuthenticateduniverseMobilePortabilityRoute
  '/_authenticated/(universe)/mobile/pro': typeof AuthenticateduniverseMobileProRoute
  '/_authenticated/(universe)/mobile/sim': typeof AuthenticateduniverseMobileSimRoute
  '/_authenticated/(universe)/mobile/terminals': typeof AuthenticateduniverseMobileTerminalsRoute
  '/_authenticated/(universe)/tickets/$id': typeof AuthenticateduniverseTicketsIdRoute
  '/_authenticated/(universe)/tickets/fleets': typeof AuthenticateduniverseTicketsFleetsRoute
  '/_authenticated/(universe)/tickets/mobile': typeof AuthenticateduniverseTicketsMobileRoute
  '/sentry/_sentry/crash/1': typeof SentrySentryCrash1Route
  '/sentry/_sentry/crash/2': typeof SentrySentryCrash2Route
  '/sentry/_sentry/crash/3': typeof SentrySentryCrash3Route
  '/_authenticated/(universe)/fiber/': typeof AuthenticateduniverseFiberIndexRoute
  '/_authenticated/(universe)/fiber/orders/$id': typeof AuthenticateduniverseFiberOrdersIdRoute
  '/_authenticated/(universe)/tickets/fiber/$id': typeof AuthenticateduniverseTicketsFiberIdRoute
  '/_authenticated/(universe)/fiber/orders/': typeof AuthenticateduniverseFiberOrdersIndexRoute
  '/_authenticated/(universe)/tickets/fiber/': typeof AuthenticateduniverseTicketsFiberIndexRoute
  '/_authenticated/(universe)/tech/fiber/orders/$id': typeof AuthenticateduniverseTechFiberOrdersIdRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/offline'
    | '/sentry'
    | '/email-confirm'
    | '/login'
    | '/logout'
    | '/register'
    | '/admin'
    | '/dashboard'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/'
    | '/reset-password'
    | '/set-password'
    | '/sentry/'
    | '/clients/companies'
    | '/clients/invoices'
    | '/clients/orders'
    | '/clients/quotes'
    | '/fiber/blocks'
    | '/fiber/eligibility'
    | '/fiber/interventions'
    | '/fiber/park-management'
    | '/mobile/fleets'
    | '/mobile/portability'
    | '/mobile/pro'
    | '/mobile/sim'
    | '/mobile/terminals'
    | '/tickets/$id'
    | '/tickets/fleets'
    | '/tickets/mobile'
    | '/sentry/crash/1'
    | '/sentry/crash/2'
    | '/sentry/crash/3'
    | '/fiber'
    | '/fiber/orders/$id'
    | '/tickets/fiber/$id'
    | '/fiber/orders'
    | '/tickets/fiber'
    | '/tech/fiber/orders/$id'
  fileRoutesByTo: FileRoutesByTo
  to:
    | ''
    | '/offline'
    | '/sentry'
    | '/email-confirm'
    | '/login'
    | '/logout'
    | '/register'
    | '/admin'
    | '/dashboard'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/'
    | '/reset-password'
    | '/set-password'
    | '/clients/companies'
    | '/clients/invoices'
    | '/clients/orders'
    | '/clients/quotes'
    | '/fiber/blocks'
    | '/fiber/eligibility'
    | '/fiber/interventions'
    | '/fiber/park-management'
    | '/mobile/fleets'
    | '/mobile/portability'
    | '/mobile/pro'
    | '/mobile/sim'
    | '/mobile/terminals'
    | '/tickets/$id'
    | '/tickets/fleets'
    | '/tickets/mobile'
    | '/sentry/crash/1'
    | '/sentry/crash/2'
    | '/sentry/crash/3'
    | '/fiber'
    | '/fiber/orders/$id'
    | '/tickets/fiber/$id'
    | '/fiber/orders'
    | '/tickets/fiber'
    | '/tech/fiber/orders/$id'
  id:
    | '__root__'
    | '/_auth'
    | '/_authenticated'
    | '/offline'
    | '/sentry'
    | '/sentry/_sentry'
    | '/_auth/email-confirm'
    | '/_auth/login'
    | '/_auth/logout'
    | '/_auth/register'
    | '/_authenticated/admin'
    | '/_authenticated/dashboard'
    | '/(errors)/401'
    | '/(errors)/403'
    | '/(errors)/404'
    | '/(errors)/500'
    | '/(errors)/503'
    | '/_authenticated/'
    | '/_auth/(password)/reset-password'
    | '/_auth/(password)/set-password'
    | '/sentry/_sentry/'
    | '/_authenticated/(universe)/clients/companies'
    | '/_authenticated/(universe)/clients/invoices'
    | '/_authenticated/(universe)/clients/orders'
    | '/_authenticated/(universe)/clients/quotes'
    | '/_authenticated/(universe)/fiber/blocks'
    | '/_authenticated/(universe)/fiber/eligibility'
    | '/_authenticated/(universe)/fiber/interventions'
    | '/_authenticated/(universe)/fiber/park-management'
    | '/_authenticated/(universe)/mobile/fleets'
    | '/_authenticated/(universe)/mobile/portability'
    | '/_authenticated/(universe)/mobile/pro'
    | '/_authenticated/(universe)/mobile/sim'
    | '/_authenticated/(universe)/mobile/terminals'
    | '/_authenticated/(universe)/tickets/$id'
    | '/_authenticated/(universe)/tickets/fleets'
    | '/_authenticated/(universe)/tickets/mobile'
    | '/sentry/_sentry/crash/1'
    | '/sentry/_sentry/crash/2'
    | '/sentry/_sentry/crash/3'
    | '/_authenticated/(universe)/fiber/'
    | '/_authenticated/(universe)/fiber/orders/$id'
    | '/_authenticated/(universe)/tickets/fiber/$id'
    | '/_authenticated/(universe)/fiber/orders/'
    | '/_authenticated/(universe)/tickets/fiber/'
    | '/_authenticated/(universe)/tech/fiber/orders/$id'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  AuthRoute: typeof AuthRouteWithChildren
  AuthenticatedRoute: typeof AuthenticatedRouteWithChildren
  OfflineRoute: typeof OfflineRoute
  SentryRoute: typeof SentryRouteWithChildren
  errors401LazyRoute: typeof errors401LazyRoute
  errors403LazyRoute: typeof errors403LazyRoute
  errors404LazyRoute: typeof errors404LazyRoute
  errors500LazyRoute: typeof errors500LazyRoute
  errors503LazyRoute: typeof errors503LazyRoute
}

const rootRouteChildren: RootRouteChildren = {
  AuthRoute: AuthRouteWithChildren,
  AuthenticatedRoute: AuthenticatedRouteWithChildren,
  OfflineRoute: OfflineRoute,
  SentryRoute: SentryRouteWithChildren,
  errors401LazyRoute: errors401LazyRoute,
  errors403LazyRoute: errors403LazyRoute,
  errors404LazyRoute: errors404LazyRoute,
  errors500LazyRoute: errors500LazyRoute,
  errors503LazyRoute: errors503LazyRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_auth",
        "/_authenticated",
        "/offline",
        "/sentry",
        "/(errors)/401",
        "/(errors)/403",
        "/(errors)/404",
        "/(errors)/500",
        "/(errors)/503"
      ]
    },
    "/_auth": {
      "filePath": "_auth.tsx",
      "children": [
        "/_auth/email-confirm",
        "/_auth/login",
        "/_auth/logout",
        "/_auth/register",
        "/_auth/(password)/reset-password",
        "/_auth/(password)/set-password"
      ]
    },
    "/_authenticated": {
      "filePath": "_authenticated.tsx",
      "children": [
        "/_authenticated/admin",
        "/_authenticated/dashboard",
        "/_authenticated/",
        "/_authenticated/(universe)/clients/companies",
        "/_authenticated/(universe)/clients/invoices",
        "/_authenticated/(universe)/clients/orders",
        "/_authenticated/(universe)/clients/quotes",
        "/_authenticated/(universe)/fiber/blocks",
        "/_authenticated/(universe)/fiber/eligibility",
        "/_authenticated/(universe)/fiber/interventions",
        "/_authenticated/(universe)/fiber/park-management",
        "/_authenticated/(universe)/mobile/fleets",
        "/_authenticated/(universe)/mobile/portability",
        "/_authenticated/(universe)/mobile/pro",
        "/_authenticated/(universe)/mobile/sim",
        "/_authenticated/(universe)/mobile/terminals",
        "/_authenticated/(universe)/tickets/$id",
        "/_authenticated/(universe)/tickets/fleets",
        "/_authenticated/(universe)/tickets/mobile",
        "/_authenticated/(universe)/fiber/",
        "/_authenticated/(universe)/fiber/orders/$id",
        "/_authenticated/(universe)/tickets/fiber/$id",
        "/_authenticated/(universe)/fiber/orders/",
        "/_authenticated/(universe)/tickets/fiber/",
        "/_authenticated/(universe)/tech/fiber/orders/$id"
      ]
    },
    "/offline": {
      "filePath": "offline.tsx"
    },
    "/sentry": {
      "filePath": "sentry/_sentry",
      "children": [
        "/sentry/_sentry"
      ]
    },
    "/sentry/_sentry": {
      "filePath": "sentry/_sentry/__route.tsx",
      "parent": "/sentry",
      "children": [
        "/sentry/_sentry/",
        "/sentry/_sentry/crash/1",
        "/sentry/_sentry/crash/2",
        "/sentry/_sentry/crash/3"
      ]
    },
    "/_auth/email-confirm": {
      "filePath": "_auth/email-confirm.tsx",
      "parent": "/_auth"
    },
    "/_auth/login": {
      "filePath": "_auth/login.tsx",
      "parent": "/_auth"
    },
    "/_auth/logout": {
      "filePath": "_auth/logout.tsx",
      "parent": "/_auth"
    },
    "/_auth/register": {
      "filePath": "_auth/register.tsx",
      "parent": "/_auth"
    },
    "/_authenticated/admin": {
      "filePath": "_authenticated/admin.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/dashboard": {
      "filePath": "_authenticated/dashboard.tsx",
      "parent": "/_authenticated"
    },
    "/(errors)/401": {
      "filePath": "(errors)/401.lazy.tsx"
    },
    "/(errors)/403": {
      "filePath": "(errors)/403.lazy.tsx"
    },
    "/(errors)/404": {
      "filePath": "(errors)/404.lazy.tsx"
    },
    "/(errors)/500": {
      "filePath": "(errors)/500.lazy.tsx"
    },
    "/(errors)/503": {
      "filePath": "(errors)/503.lazy.tsx"
    },
    "/_authenticated/": {
      "filePath": "_authenticated/index.tsx",
      "parent": "/_authenticated"
    },
    "/_auth/(password)/reset-password": {
      "filePath": "_auth/(password)/reset-password.tsx",
      "parent": "/_auth"
    },
    "/_auth/(password)/set-password": {
      "filePath": "_auth/(password)/set-password.tsx",
      "parent": "/_auth"
    },
    "/sentry/_sentry/": {
      "filePath": "sentry/_sentry/index.tsx",
      "parent": "/sentry/_sentry"
    },
    "/_authenticated/(universe)/clients/companies": {
      "filePath": "_authenticated/(universe)/clients/companies.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/clients/invoices": {
      "filePath": "_authenticated/(universe)/clients/invoices.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/clients/orders": {
      "filePath": "_authenticated/(universe)/clients/orders.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/clients/quotes": {
      "filePath": "_authenticated/(universe)/clients/quotes.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/fiber/blocks": {
      "filePath": "_authenticated/(universe)/fiber/blocks.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/fiber/eligibility": {
      "filePath": "_authenticated/(universe)/fiber/eligibility.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/fiber/interventions": {
      "filePath": "_authenticated/(universe)/fiber/interventions.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/fiber/park-management": {
      "filePath": "_authenticated/(universe)/fiber/park-management.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/mobile/fleets": {
      "filePath": "_authenticated/(universe)/mobile/fleets.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/mobile/portability": {
      "filePath": "_authenticated/(universe)/mobile/portability.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/mobile/pro": {
      "filePath": "_authenticated/(universe)/mobile/pro.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/mobile/sim": {
      "filePath": "_authenticated/(universe)/mobile/sim.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/mobile/terminals": {
      "filePath": "_authenticated/(universe)/mobile/terminals.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/tickets/$id": {
      "filePath": "_authenticated/(universe)/tickets/$id.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/tickets/fleets": {
      "filePath": "_authenticated/(universe)/tickets/fleets.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/tickets/mobile": {
      "filePath": "_authenticated/(universe)/tickets/mobile.tsx",
      "parent": "/_authenticated"
    },
    "/sentry/_sentry/crash/1": {
      "filePath": "sentry/_sentry/crash/1.tsx",
      "parent": "/sentry/_sentry"
    },
    "/sentry/_sentry/crash/2": {
      "filePath": "sentry/_sentry/crash/2.tsx",
      "parent": "/sentry/_sentry"
    },
    "/sentry/_sentry/crash/3": {
      "filePath": "sentry/_sentry/crash/3.tsx",
      "parent": "/sentry/_sentry"
    },
    "/_authenticated/(universe)/fiber/": {
      "filePath": "_authenticated/(universe)/fiber/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/fiber/orders/$id": {
      "filePath": "_authenticated/(universe)/fiber/orders/$id.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/tickets/fiber/$id": {
      "filePath": "_authenticated/(universe)/tickets/fiber/$id.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/fiber/orders/": {
      "filePath": "_authenticated/(universe)/fiber/orders/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/tickets/fiber/": {
      "filePath": "_authenticated/(universe)/tickets/fiber/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/(universe)/tech/fiber/orders/$id": {
      "filePath": "_authenticated/(universe)/tech/fiber/orders/$id.tsx",
      "parent": "/_authenticated"
    }
  }
}
ROUTE_MANIFEST_END */
