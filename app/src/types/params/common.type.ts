// api
import type { TFiberOrderAcquisition, TFiberOrderSLA, TFiberOrderState, TFiberOrderType } from '@/api/interface/fiber';
import type { TFiberOrderFixCategory } from '@/api/interface/fiber';

// ----------------------------------------------------------------------

export type TPaginationParams = {
	page?: number;
	itemsPerPage?: number;
};

export type TCreatedAtParams = {
	'createdAt[before]': string | Date;
	'createdAt[strictly_before]': string | Date;
	'createdAt[after]': string | Date;
	'createdAt[strictly_after]': string | Date;
};

export type TUpdatedAtParams = {
	'updatedAt[before]': string | Date;
	'updatedAt[strictly_before]': string | Date;
	'updatedAt[after]': string | Date;
	'updatedAt[strictly_after]': string | Date;
};

export type TDeletedAtParams = {
	'deletedAt[before]': string | Date;
	'deletedAt[strictly_before]': string | Date;
	'deletedAt[after]': string | Date;
	'deletedAt[strictly_after]': string | Date;
};

export type TSortByFetcherParams = 'asc' | 'desc';

// TODO: deplacer dans reactore

// Fiber -----------------------------------

export type TFiberGetOrdersParams = Partial<
	TPaginationParams &
		TCreatedAtParams &
		TUpdatedAtParams &
		TDeletedAtParams & {
			'order[createdAt]': TSortByFetcherParams;
			'order[updatedAt]': TSortByFetcherParams;
			'order[deletedAt]': TSortByFetcherParams;
			reference: string[];
			retailer: string[];
			ois: {
				id: number[];
			};
			businessProject: string[];
			orderFilter: string;
			state: TFiberOrderState;
		}
>;

export type TFiberGetCommunicationParams = Partial<
	TPaginationParams &
		TCreatedAtParams & {
			'order[createdAt]': TSortByFetcherParams;
			comment: string;
		}
>;
export type TFiberGetCompaniesParams = Partial<
	TPaginationParams &
		TCreatedAtParams & {
			'cellUri[]': string[];
			cellUri: string;
		}
>;

export type TFiberGetAppoinmentParams = Partial<TPaginationParams & TCreatedAtParams>;
export type TFiberMetricsAppoinmentCountParams = {
	from: string | Date;
	to: string | Date;
	filter?: 'states' | 'types' | 'technologies' | 'offers' | 'speeds' | 'uprs';
	onlyPast?: boolean;
	export?: boolean;
};

export type TFiberGetOrderIncidentParams = Partial<
	TPaginationParams &
		TCreatedAtParams &
		TUpdatedAtParams &
		TDeletedAtParams & {
			'order[createdAt]': string;
			'order[updatedAt]': string;
			'order[deletedAt]': string;
			reference: string | string[];
			retailer: string | string[];
			ois: {
				id: string | string[];
			};
			businessProject: string | string[];
			orderFilter: string;
			state: string;
		}
>;
export type TFiberGetPlanningParams = Partial<{ duration: number; from: string; to: string }>;

export type TFiberPostAppointmentScheduleParams = Partial<{
	grdv_user_id: number[];
	date: string;
	startAt: string;
	duration: number;
}>;

export type TFiberGetOrderFixParams = Partial<
	TPaginationParams & {
		category: TFiberOrderFixCategory;
		'category[]': TFiberOrderFixCategory;
	}
>;

export type TFiberPutOrderParams = Partial<{
	type: TFiberOrderType;
	reference: string;
	retailerReference: string;
	customerReference: string;
	b2cReference: string;
	allowMigration: boolean;
	autoMode: boolean;
	autoFix: boolean;
	extraTracking: boolean;
	sla: TFiberOrderSLA;
	acquisition: TFiberOrderAcquisition;
	expectedAt: string;
	endpointBuilding: {
		imb: string | null;
	};
	businessProject: string;
	deliveryAddress: string;
	endpointAddress: {
		iwId: number;
	};
	ois: [
		{
			'@id': string;
			ptoName: string;
			pmName: string;
			pmTechnicalName: string;
			floorName: string;
			stairCaseName: string;
			buildingName: string;
		}
	];
	incidents: string[];
	products: string[];
	contacts: Record<string, any>[];
	offers: string;
	company: {
		name: string;
	};
}>;

// Cell -----------------------------------
export type TCellPeriodParam = Partial<{ period: string | Date }>;
export type TCellFleetParam = Partial<{ fleet: string }>;

export type TCellPortageParams = Partial<
	TPaginationParams & {
		state: string | string[];
		msisdn: string | string[];
	}
>;

export type TCellNotificationsParams = Partial<
	TPaginationParams &
		TCreatedAtParams & {
			level: string | string[];
			showOnDashboard: boolean;
			readOnDashboard: boolean;
		}
>;

export type TCellSimCardParams = Partial<
	TPaginationParams & {
		iccid: string;
		'exists[line]': boolean;
	}
>;

export type TCellDeviceParams = Partial<
	TPaginationParams & {
		imei?: string | string[];
	}
>;

export type TCellOutgoingPortasParams = Partial<
	TCellPortageParams & {
		'company.retailerCompany.retailer': string | string[];
	}
>;
