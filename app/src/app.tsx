import { Suspense } from 'react';
// tanstack
import { RouterProvider } from '@tanstack/react-router';
// utils
import * as Sentry from '@sentry/react';
// contexts
import { AuthConsumer, useAuthContext } from './auth/auth-context';
// others
import { queryClient } from './services/query-client.service';
import { router } from './services/router.service';
import { CustomLoading } from './components/custom';

// ----------------------------------------------------------------------

function App() {
	const auth = useAuthContext();

	return (
		<Suspense fallback={<CustomLoading />}>
			<AuthConsumer>
				<RouterProvider
					defaultOnCatch={(error) => {
						Sentry.captureException(error);
					}}
					context={{
						auth,
						queryClient
					}}
					router={router}
				/>
			</AuthConsumer>
		</Suspense>
	);
}

export default Sentry.withProfiler(App, { name: 'App' });
