import { createContext, useContext, useState, useEffect, type ReactNode } from 'react';
// tanstack
import { useQuery } from '@tanstack/react-query';
import { useLocation } from '@tanstack/react-router';
// utils
import type { TNameIcons } from '@tools/reactor-icons';
// api
import type { IOrderItem } from '@/api/interface';
import { fiberQueryKeys, magnetQueryKeys } from '@/api/queries';
// sections
import Appointments from '@/sections/universe/tickets/fiber/_components/tickets-details/right-panel/tabs/appointment';
import CallHistory from '@/sections/universe/tickets/fiber/_components/tickets-details/right-panel/tabs/call-history';
import LineDetails from '@/sections/universe/tickets/fiber/_components/tickets-details/right-panel/tabs/line-details';
import LineTypology from '@/sections/universe/tickets/fiber/_components/tickets-details/right-panel/tabs/line-typology';
import OtherTickets from '@/sections/universe/tickets/fiber/_components/tickets-details/right-panel/tabs/other-tickets';

// ----------------------------------------------------------------------

type TabId = 1 | 2 | 3 | 4 | 5;

interface TabConfig {
	id: TabId;
	icon: TNameIcons;
	label: string;
	component: React.ReactNode;
}

interface TicketRightPanelState {
	isExpanded?: string;
	selectedTab: TabId;
}

interface TicketRightPanelContextType extends TicketRightPanelState {
	tabs: TabConfig[];
	activeTabComponent: React.ReactNode;
	isTicketDetail: boolean;
	toggleExpanded: (expandedView: string) => () => void;
	selectTab: (tabId: TabId) => void;
	openWithTab: (tabId: TabId, expandedView?: string) => void;
	orderData?: IOrderItem;
	setReference: (ref: string) => void;
}

const INITIAL_STATE: TicketRightPanelState = {
	isExpanded: undefined,
	selectedTab: 1
};

const TicketRightPanelContext = createContext<TicketRightPanelContextType | undefined>(undefined);

export const TicketRightPanelProvider = ({ children }: { children: ReactNode }) => {
	const [state, setState] = useState<TicketRightPanelState>(INITIAL_STATE);
	const location = useLocation();
	const [activeTicket, setActiveTicket] = useState<string | undefined>('');
	const [reference, setReference] = useState<string | undefined>(undefined);

	const pathParts = location.pathname.split('/');
	const isTicketDetail =
		location.pathname.startsWith('/tickets/') &&
		location.pathname !== '/tickets' &&
		!(pathParts.length === 3 && (pathParts[2] === 'fiber' || pathParts[2] === 'mobile')) &&
		pathParts.length > 3;

	const { data: ticketData } = useQuery({
		...magnetQueryKeys.magnet_ticket.byId(activeTicket),
		enabled: Boolean(activeTicket && isTicketDetail)
	});

	const { data: orderData } = useQuery({
		...fiberQueryKeys.fiber_order.byId(reference),
		enabled: Boolean(reference)
	});

	const TABS: TabConfig[] = [
		{
			id: 1,
			icon: 'File02',
			label: 'Détail de la ligne',
			component: <LineDetails />
		},
		{
			id: 2,
			icon: 'Rows03',
			label: 'Typologie de la ligne',
			component: <LineTypology />
		},
		{
			id: 3,
			icon: 'CalendarDate',
			label: 'Randez-vous',
			component: <Appointments />
		},
		{
			id: 4,
			icon: 'Ticket01',
			label: 'Autres tickets',
			component: <OtherTickets />
		},
		{
			id: 5,
			icon: 'Phone',
			label: "Historique d'appels",
			component: <CallHistory />
		}
	];

	useEffect(() => {
		if (ticketData?.reference) setReference(ticketData?.reference);
	}, [ticketData]);

	useEffect(() => {
		if (isTicketDetail) {
			const lastPathPart = pathParts[pathParts.length - 1];
			if (lastPathPart && /^\d+$/.test(lastPathPart)) {
				setActiveTicket(lastPathPart);
			} else {
				setActiveTicket(undefined);
			}
		} else {
			setActiveTicket(undefined);
		}
	}, [location.pathname, isTicketDetail, pathParts]);

	useEffect(() => {
		if (!isTicketDetail) {
			setState(INITIAL_STATE);
		}
	}, [isTicketDetail]);

	const openWithTab = (tabId: TabId, expandedView?: string) => {
		setState({
			isExpanded: expandedView,
			selectedTab: tabId
		});
	};

	const toggleExpanded = (expandedView: string) => () => {
		setState((prev) => ({
			...prev,
			isExpanded: prev?.isExpanded ? undefined : expandedView
		}));
	};

	const selectTab = (tabId: TabId) => {
		setState((prev) => ({
			...prev,
			selectedTab: tabId
		}));
	};

	const activeTab = TABS.find((tab) => tab.id === state.selectedTab);

	const value = {
		...state,
		tabs: TABS,
		activeTabComponent: activeTab?.component,
		toggleExpanded,
		isTicketDetail: isTicketDetail,
		selectTab,
		openWithTab,
		orderData,
		setReference
	};

	return <TicketRightPanelContext.Provider value={value}>{children}</TicketRightPanelContext.Provider>;
};

export const useTicketRightPanel = () => {
	const context = useContext(TicketRightPanelContext);
	if (!context) {
		throw new Error('useTicketRightPanel must be used within a TicketRightPanelProvider');
	}
	return context;
};
