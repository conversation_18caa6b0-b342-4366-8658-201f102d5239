// tanstack
import { type NavigateOptions, useLocation, useNavigate, useParams, useSearch } from '@tanstack/react-router';

// ----------------------------------------------------------------------

/**
 * @deprecated
 */
const useRouting = () => {
	const { pathname } = useLocation();
	const navigate = useNavigate();
	const params = useParams({ strict: false });
	let query = useSearch({ strict: false });

	query = {
		...query,
		// @ts-ignore: to remove after migration
		get(key: keyof typeof query) {
			return this?.[key];
		}
	};

	const push = (to: NavigateOptions['to'], option?: Omit<NavigateOptions, 'to'>) => {
		return navigate({
			to,
			...option
		});
	};

	const replace = (to: NavigateOptions['to'], option?: Omit<NavigateOptions, 'to' | 'replace'>) => {
		return navigate({
			to,
			replace: true,
			...option
		});
	};

	return {
		pathname,
		push,
		replace,
		query,
		params
	};
};

export default useRouting;
