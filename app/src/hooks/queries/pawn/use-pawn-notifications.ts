// tanstack
import { useQuery } from '@tanstack/react-query';
// utils
import { isUndefined } from 'lodash';
// api
import type { IPawnNotification } from '@/api/interface/pawn';
import { getHydra } from '@/api/fetcher';
import { pawnQueryKeys } from '@/api/queries';
// hooks
import { useUserAtom } from '@/hooks/atom';
// types
import type { TPawnNotificationsParams } from '@/types';

// ----------------------------------------------------------------------

type UsePawnNotificationsParams = {
	params?: TPawnNotificationsParams;
	options?: Record<string, any>;
};
const usePawnNotifications = (config?: UsePawnNotificationsParams) => {
	const { params, options = {} } = config || {};
	const {
		user: { id } = {}
	} = useUserAtom();

	const query = useQuery({
		...pawnQueryKeys.pawn_staff.byId(id)._ctx.notifications(params),
		select: (data) => getHydra<IPawnNotification>(data),
		...options,
		enabled: () => {
			if (options && !isUndefined(options?.enabled)) {
				return Boolean(options?.enabled) && Boolean(id);
			}

			return Boolean(id);
		}
	});

	const { data } = query;

	const notifications = data?.items;
	const notificationsFirstItem = data?.items?.length ? data.items[0] : undefined;
	const notificationsLastItem = data?.items?.length ? data.items[data.items.length - 1] : undefined;
	const notificationsTotal = data?.total;

	return {
		notifications,
		notificationsTotal,
		notificationsFirstItem,
		notificationsLastItem,
		...query,
		invalidQueryKey: pawnQueryKeys.pawn_staff.byId(id)._ctx.notifications._def
	};
};

export default usePawnNotifications;
