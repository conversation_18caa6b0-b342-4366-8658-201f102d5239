// tanstack
import { useQuery } from '@tanstack/react-query';
// api
import { queries } from '@/api/queries';
// hooks
import { useUserAtom } from '@/hooks/atom';

// ----------------------------------------------------------------------

const useMagnetStaff = () => {
	const { user } = useUserAtom();
	//
	const res = useQuery({ ...queries.magnet_staff.byId(user?.uid), enabled: <PERSON><PERSON><PERSON>(user?.uid) });
	// const res = useQuery({ ...queries.magnet_staff.me() })
	return {
		...res
	};
};
export default useMagnetStaff;
