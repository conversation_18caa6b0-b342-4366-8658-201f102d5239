// tanstack
import type { UseQueryOptions, UseQueryResult } from '@tanstack/react-query/src/types';
import { useQuery } from '@tanstack/react-query';
// utils
import type { TFetcherConfig } from '@tools/reactore';

// ----------------------------------------------------------------------

export type TQueryKeyResult = [string | undefined, string | Record<any, any> | undefined, TFetcherConfig];
export type TQueryHook = <TData, TError = unknown>(
	params: TQueryHookParams<TData>,
	typeFetch?: TTypeApiFetcher
) => UseQueryResult<TData, TError> & { queryKey: TQueryKeyResult };

export type TQueryHookParams<TData> = {
	url?: string;
	params?: string | Record<any, any>;
	configFetch?: TFetcherConfig;
	options: Omit<UseQueryOptions<unknown, unknown, TData>, 'queryKey' | 'queryFn'>;
};
export type TTypeApiFetcher = 'cell' | 'fiber' | 'magnet';

// @ts-ignore
export const useQueryDf: TQueryHook = <TData,>(
	{ url, params, configFetch = {}, options = {} }: TQueryHookParams<TData>,
	typeFetch: TTypeApiFetcher = 'fiber'
) => {
	const configFetchCurrrent = {
		prefixURL: typeFetch,
		...configFetch
	};

	const queryKey: TQueryKeyResult = [url, params, configFetchCurrrent];

	// @ts-ignore
	const query = useQuery<TData>(queryKey, options);
	return {
		...query,
		queryKey
	};
};
