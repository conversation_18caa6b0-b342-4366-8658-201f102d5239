// utils
import type { IMagnetTicket } from '@/api/schemas/entities';
import { arrayfindIndex } from '@/utils/common.utils';
import { useAtom } from 'jotai';
import { atomWithStorage } from 'jotai/utils';

// ----------------------------------------------------------------------

const historikTicketsAtom = atomWithStorage<IMagnetTicket[]>('historikTickets', []);

export type THistoryTickets = {
	action: 'put' | 'delete';
	ticket: IMagnetTicket;
};

export const useHistoryTickets = () => {
	const [historikTickets, setHistorikTickets] = useAtom(historikTicketsAtom);

	const reducer = ({ action, ticket }: THistoryTickets) => {
		switch (action) {
			case 'put':
				if (!historikTickets.find((ticketElem) => ticketElem['@id'] === ticket['@id'])) {
					if (historikTickets.length < 5) setHistorikTickets([ticket, ...historikTickets]);
					else if (historikTickets.length === 5) {
						historikTickets.pop();
						setHistorikTickets([ticket, ...historikTickets]);
					}
				} else {
					const index = arrayfindIndex(historikTickets, ticket['@id'], '@id');
					if (index) {
						historikTickets.splice(index, 1);
						setHistorikTickets([ticket, ...historikTickets]);
					}
				}
				break;
			case 'delete':
				setHistorikTickets([]);
				break;
			default:
				break;
		}
	};

	return { historikTickets, setHistorikTickets: reducer };
};
