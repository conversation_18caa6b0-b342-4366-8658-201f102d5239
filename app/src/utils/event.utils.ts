export enum Events {
	SET_TITLE = 'set_title'
	// use an enum to keep track of events similar to action types set as variables in redux
}
export const eventEmitter = {
	_events: {},
	dispatch(event: string, data: any) {
		if (!this._events[event]) return;
		this._events[event].forEach((callback: (arg0: any) => any) => callback(data));
	},
	subscribe(event: string, callback: (data: any) => void) {
		if (!this._events[event]) this._events[event] = [];
		this._events[event].push(callback);
	},
	unsubscribe(event: string) {
		if (!this._events[event]) return;
		delete this._events[event];
	},
	hasSubscribe(event: string) {
		return !!this._events[event];
	}
};
