// utils
import { capitalizeSentCase } from './change-case';
// api
import type { ICellAddress } from '@/api/interface';

// ----------------------------------------------------------------------

export function fPhoneNumber(phoneNumber: string | undefined) {
	if (!phoneNumber) {
		return '';
	}

	// normalize string and remove all unnecessary characters
	phoneNumber = phoneNumber.replace(/[^\d]/g, '');

	// add initial 0 if not present
	if (phoneNumber.length < 10) {
		phoneNumber = `0${phoneNumber}`;
	}

	if (phoneNumber.length >= 11) {
		// replace initial 33
		phoneNumber = phoneNumber.replace(/^33/, '0');
	}

	return phoneNumber.replace(/(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4 $5');
}

export function fNormalizePhoneNumber(phoneNumber: string | undefined) {
	if (!phoneNumber) {
		return '';
	}

	// remove all spaces
	phoneNumber = phoneNumber.replace(/\s/g, '');

	// normalize string and remove all unnecessary characters
	phoneNumber = phoneNumber.replace(/[^\d]/g, '');

	// remove + if present
	phoneNumber = phoneNumber.replace(/^\+/, '');

	return phoneNumber;
}

export function formatShopName(name?: string) {
	if (!name) return '';
	return capitalizeSentCase(name);
}

export function formatAddress(address?: ICellAddress | undefined | null) {
	if (!address) return '';

	const { city, postCode, street } = address;

	const blocks = [street?.toLowerCase(), `${postCode} ${city}`].filter((block) => !!block);

	return blocks.join(', ');
}
