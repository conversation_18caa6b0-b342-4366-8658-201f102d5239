// utils
import { http, HttpResponse } from 'msw';
// others
import { metricsAppointments } from '@/mocks/fixtures/metrics';

// ----------------------------------------------------------------------

export const getMetricsAppointmentHandler = http.get('*/api/fiber/metrics/appointments/count', (req, res, ctx) => {
	return res(
		HttpResponse.json
			metricsAppointments(
				new Date(req.url.searchParams.get('from') || 'now'),
				new Date(req.url.searchParams.get('to') || 'now')
			)
		)
	)
});
