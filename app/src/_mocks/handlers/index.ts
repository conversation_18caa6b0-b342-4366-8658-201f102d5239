// import fiberHandlers from './fiber'
// import { getAppointmentHandler } from './fiber/appointment/getAppointments.handler'
// import { getMetricsAppointmentHandler } from './fiber/metrics/getMetricsAppointments.handler'
// import { getOrderCallAttemptsHandler } from './fiber/order/getOrderCallAttempts.handler'
// import { getOrderCompanyContactsHandler } from './fiber/order/getOrderCompanyContacts.handler'
// import { getOrderInvoicesHandler } from './fiber/order/getOrderInvoices.handler'
// import { getOrderLogisticOrdersHandler } from './fiber/order/getOrderLogisticOrders.handler'
// import { getOrderNetworkHistoryHandler } from './fiber/order/getOrderNetworkHistory.handler'
// import { getOrderNetworksHandler } from './fiber/order/getOrderNetworks.handler'
// import { getOrderOiHistoryHandler } from './fiber/order/getOrderOiHistory.handler'
// import { getOrderOisHandler } from './fiber/order/getOrderOis.handler'
// import { getOrderTelemetriesHandler } from './fiber/order/getOrderTelemetries.handler'
// import { getOrderVlanHistoryHandler } from './fiber/order/getOrderVlanHistory.handler'
// import magnetHandlers from './magnet'
// import { postPawnStaffLoginHandler } from './pawn/staff.handler'
// import { getPawnUsersHandler } from './pawn/users/getUsers.handler'
// import { flatMap } from 'lodash'
//
// export default flatMap([
// 	...fiberHandlers,
// 	...magnetHandlers,
// 	getAppointmentHandler,
// 	getOrderLogisticOrdersHandler,
// 	getOrderCallAttemptsHandler,
// 	getOrderCompanyContactsHandler,
// 	getOrderInvoicesHandler,
// 	getOrderOisHandler,
// 	getOrderOiHistoryHandler,
// 	getOrderNetworksHandler,
// 	getOrderNetworkHistoryHandler,
// 	getOrderVlanHistoryHandler,
// 	getOrderTelemetriesHandler,
// 	getPawnUsersHandler,
// 	getMetricsAppointmentHandler,
// 	postPawnStaffLoginHandler
// ])
