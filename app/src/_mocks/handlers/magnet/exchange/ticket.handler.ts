// utils
import { http, HttpResponse } from 'msw';
// others
import { listSerializer } from '@/mocks/ctx/list-response';
import { getTicket, tickets } from '@/mocks/fixtures/magnet';

// ----------------------------------------------------------------------

export const getTicketHandler = http.get('*/magnet/exchanges/tickets/:id', (_req, _res, ctx) => {
	return _res(HttpResponse.jsongetTicket(`/exchanges/tickets/${_req.params.id}`));
	)
});
export const getTicketsHandler = http.get('*/magnet/exchanges/tickets', (_req, _res) => {
	return _res(listSerializer(tickets));
});

const exchangeTicketHandler = [getTicketHandler, getTicketsHandler];
export default exchangeTicketHandler;
