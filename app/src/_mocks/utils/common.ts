// utils
import * as jose from 'jose';
import sjcl from 'sjcl';
// others
import type { IColors } from '@/entities/fiber';

// ----------------------------------------------------------------------

/**
 * @param {Omit<IColors, "@id" | "@context" | "@type">} data
 * @returns {IColors}
 */
export const colorsEntity = (data: Omit<IColors, '@id' | '@context' | '@type'>): IColors => {
	return {
		'@id': 'colors/1',
		'@context': 'string',
		'@type': 'Colors',
		...data
	};
};

/**
 * @param {string} iri
 * @param {any[]} entities
 * @returns {any}
 */
export const getEntityBy = (iri: string, entities: any[]): any => {
	return entities.find((entity) => entity['@id'] === iri);
};

/**
 * @link sjcl {http://bitwiseshiftleft.github.io/sjcl/doc/sjcl.json.html}
 * @param data
 * @param type
 * @returns {sjcl.SjclCipherEncrypted}
 */
export const crypt = (data: string, type: 'encrypt' | 'decrypt'): string => {
	const secret = '94ac813e6bbcd5428f35c1696e37d9e2eaf8a53fb31909f2b6e33ad63762d964';

	if (type === 'decrypt') {
		return sjcl.decrypt(secret, data);
	}

	return sjcl.encrypt(secret, data) as unknown as string;
};

const secret = new TextEncoder().encode('cc7e0d44fd473002f1c42167459001140ec6389b7353f8088f4d9a95f2f596f2');
const alg = 'HS256';

export const jwtEncode = async (data: jose.JWTPayload) => {
	return new jose.SignJWT(data)
		.setProtectedHeader({
			alg
		})
		.setIssuer('urn:example:issuer')
		.setExpirationTime('20m')
		.sign(secret);
};
export const jwtDecode = async (token: string | Uint8Array) => {
	const data = await jose
		.jwtVerify(token, secret, {
			issuer: 'urn:example:issuer',
			audience: 'urn:example:audience'
		})
		.catch((err) => {
			
			throw err;
		});

	

	return data;
};
