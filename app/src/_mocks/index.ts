// utils
import { WebSocketHandler } from 'msw';
// constants
import { NODE_ENV, VITE_MOCKING } from '@/env';

// ----------------------------------------------------------------------

export async function enableMocking() {
	if (!VITE_MOCKING || NODE_ENV === 'production') {
		return;
	}

	const { worker } = await import('./browser');

	// eslint-disable-next-line consistent-return
	return worker
		.start({
			onUnhandledRequest: 'bypass',
			quiet: false
		})
		.then((res: any) => {
			// const openDb = await require('../mocks/browser')
			//
			// openDb()
			worker.listHandlers().forEach((handler) => {
				if (!(handler instanceof WebSocketHandler)) {
					console.debug(handler.info.header);
				}
			});
			return res;
		})
		.catch((err: any) => {
			throw err;
		});
}
