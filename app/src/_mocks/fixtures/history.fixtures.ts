// utils
import { sub } from 'date-fns';
// api
import type { IFiberHistory } from '@/api/interface';
// types
import type { HydraResponse } from '@/types';

// ----------------------------------------------------------------------

const history: HydraResponse<IFiberHistory>[] = [
	{
		'@id': '',
		'@context': '',
		'@type': '',
		'hydra:Record': {},
		'hydra:member': [
			{
				'@type': 'OiWorkflow',
				createdAt: sub(new Date('2022-10-24T14:16:53+02:00'), { days: 1 }),
				fromState: 'CREATED',
				referenceOrder: {
					'@id': '/orders/1',
					'@type': 'Order',
					createdAt: sub(new Date('2022-10-24T14:16:53+02:00'), { days: 1 }),
					updatedAt: sub(new Date('2022-10-24T14:16:53+02:00'), { days: 1 })
				},
				toState: 'SENT',
				transitionName: 'send',
				updatedAt: sub(new Date('2022-10-24T14:16:53+02:00'), { days: 1 })
			},
			{
				'@type': 'OiWorkflow',
				createdAt: new Date('2022-10-24T14:16:53+02:00'),
				fromState: 'SENT',
				referenceOrder: {
					'@id': '/orders/1',
					'@type': 'Order',
					createdAt: new Date('2022-10-24T14:16:53+02:00'),
					updatedAt: new Date('2022-10-24T14:16:53+02:00')
				},
				toState: 'FULL_ROP',
				transitionName: 'checkFullRop',
				updatedAt: new Date('2022-10-24T14:16:53+02:00')
			}
		],
		'hydra:totalItems': 2
	}
];

export default history;
