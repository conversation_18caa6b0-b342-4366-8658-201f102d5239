// others
import { IPawnStaff } from '@/entities/pawn';

// ----------------------------------------------------------------------

const staffFixtures: IPawnStaff[] = [
	{
		'@context': '/contexts/Staff',
		'@id': '/staff/1',
		'@type': 'Staff',
		id: 1,
		roles: ['ROLE_STAFF'],
		createdAt: new Date('2022-07-06T00:00:11+02:00'),
		email: '<EMAIL>',
		firstName: 'staff',
		lastName: 'donter de la fixture',
		username: 'staff',
		changePassword: false
	},
	{
		'@context': '/contexts/Staff',
		'@id': '/staff/2',
		id: 2,
		'@type': 'Staff',
		roles: ['ROLE_ADMIN', 'ROLE_USER', 'ROLE_STAFF'],
		createdAt: new Date('2022-07-06T00:00:11+02:00'),
		email: '<EMAIL>',
		firstName: 'admin',
		lastName: 'donter de la fixture',
		username: 'admin',
		changePassword: false
	},
	{
		'@context': '/contexts/Staff',
		'@id': '/staff/3',
		id: 3,
		'@type': 'Staff',
		roles: ['ROLE_USER'],
		createdAt: new Date('2022-07-06T00:00:11+02:00'),
		email: '<EMAIL>',
		firstName: 'user',
		username: 'user',
		lastName: 'donter de la fixture ',
		changePassword: false
	}
];

export default staffFixtures;
