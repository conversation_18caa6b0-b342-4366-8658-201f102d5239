import { useCallback, useEffect } from 'react';
// tanstack
import { useRouter } from '@tanstack/react-router';
// contexts
import { useAuthContext } from '@/auth/auth-context';
import { CustomLoading } from '@/components/custom';

// ----------------------------------------------------------------------

type Props = {
	children: React.ReactNode;
};

export default function GuestGuard({ children }: Props) {
	const { loading } = useAuthContext();

	return <>{loading ? <CustomLoading /> : <Container>{children}</Container>}</>;
}

// ----------------------------------------------------------------------

function Container({ children }: Props) {
	const { navigate } = useRouter();

	// const searchParams = useSearchParams();
	const searchParams = new URLSearchParams(window.location.search);

	const returnTo = searchParams.get('returnTo');

	const { authenticated } = useAuthContext();

	const check = useCallback(() => {
		if (authenticated && returnTo) {
			navigate({ to: returnTo });
		}
	}, [authenticated, navigate, returnTo]);

	useEffect(() => {
		check();
	}, [check]);

	return <>{children}</>;
}
