// contexts
import { useAuthContext } from '@/auth/auth-context';
// sections
import ForbiddenError from '@/sections/errors/forbidden';

// ----------------------------------------------------------------------

type RoleBasedGuardProp = {
	capabilities?: string[];
	excludedCapabilities?: string[];
	children: React.ReactNode;
};

export default function CapabilitiesBasedGuard({ capabilities, excludedCapabilities, children }: RoleBasedGuardProp) {
	const { userDetails, isAllowed } = useAuthContext();

	let shouldSeeContent = isAllowed(capabilities!, excludedCapabilities!);

	if (userDetails?.capabilities?.includes('admin')) {
		shouldSeeContent = true;
	}

	if (!shouldSeeContent) {
		return <ForbiddenError />;
	}

	return <> {children} </>;
}
