// tanstack
import type { Column } from '@tanstack/react-table';
// utils
import { cn } from '@/lib/utils.ts';
import { ArrowDownIcon, ArrowUpIcon, ChevronsUpDown } from 'lucide-react';
// components
import { Button } from '@/components/ui/button.tsx';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu.tsx';

// ----------------------------------------------------------------------

interface DataTableColumnHeaderProps<TData, TValue> extends React.HTMLAttributes<HTMLDivElement> {
	column: Column<TData, TValue>;
	title: string;
}

export default function DataTableColumnHeader<TData, TValue>({ column, title, className }: DataTableColumnHeaderProps<TData, TValue>) {
	if (!column.getCanSort()) {
		return <div className={cn(className)}>{title}</div>;
	}

	return (
		<div className={cn('flex items-center space-x-1', className)}>
			<span>{title}</span>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant='ghost' size='sm' className='-ml-3 w-6 h-7 p-0 data-[state=open]:bg-accent'>
						{column.getIsSorted() === 'desc' ? (
							<ArrowDownIcon className='h-4 w-4' />
						) : column.getIsSorted() === 'asc' ? (
							<ArrowUpIcon className='h-4 w-4' />
						) : (
							<ChevronsUpDown className='h-4 w-4' />
						)}
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align='start'>
					<DropdownMenuItem onClick={() => column.toggleSorting(false)}>
						<ArrowUpIcon className='mr-2 h-3.5 w-3.5 text-muted-foreground/70' />
						Asc
					</DropdownMenuItem>
					<DropdownMenuItem onClick={() => column.toggleSorting(true)}>
						<ArrowDownIcon className='mr-2 h-3.5 w-3.5 text-muted-foreground/70' />
						Desc
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>
		</div>
	);
}
