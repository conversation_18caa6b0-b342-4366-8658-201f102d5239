// tanstack
import { useQuery } from '@tanstack/react-query';
// api
import { fiberQueryKeys } from '@/api/queries';

// ----------------------------------------------------------------------

export type orderFields = 'NRO' | 'address';

const isValidOrderReference = (reference: string | null | undefined): boolean => {
	if (!reference || reference.trim() === '') return false;

	return reference.includes('order') || /^[A-Z0-9]{6,}$/i.test(reference);
};

export const useOrderData = (reference: string | null | undefined) => {
	const enabled = isValidOrderReference(reference);

	const { data } = useQuery({
		...fiberQueryKeys.fiber_order.byId(reference || ''),
		enabled,
		initialData: enabled ? undefined : null
	});

	return data;
};

export const getOrderField = (orderData: any, fields: orderFields): string => {
	if (!orderData) return '';

	switch (fields) {
		case 'NRO':
			return orderData?.currentRops?.nro?.name || '';
		case 'address':
			return (
				`${orderData?.endpointAddress?.street}, ${orderData?.endpointAddress?.city} ${orderData?.endpointAddress?.postalCode}` || ''
			);
		default:
			return '';
	}
};

// DEPRECATED
export const orderFetcher = (reference: string, fields: orderFields): string => {
	console.warn('orderFetcher is deprecated as it uses hooks internally. Use useOrderData hook instead.');
	const { data } = useQuery({ ...fiberQueryKeys.fiber_order.byId(reference) });
	return getOrderField(data, fields);
};
