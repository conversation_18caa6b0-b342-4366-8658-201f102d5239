import { useState } from 'react';
// utils
import { capitalizeCase } from '@/utils/change-case';
import { format } from 'date-fns';
import { Plus, Search, X, Calendar } from 'lucide-react';
// components
import { Badge } from '../ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Checkbox } from '@/components/ui/checkbox';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

// ----------------------------------------------------------------------

export type FilterValue = string | Date;

export type FilterOption = {
	value: FilterValue;
	label: string;
	count?: number;
	icon?: React.ReactNode;
	type?: 'checkbox' | 'date';
};

export type FilterState = {
	checkboxValues: string[];
	dateValue?: Date;
};

export type CustomFilterProps = {
	title: string;
	options: FilterOption[];
	selected: FilterState;
	onChange: (value: FilterState) => void;
	onClearFilter?: (value: FilterValue) => void;
	type?: 'checkbox' | 'date' | 'mixed';
};

export function CustomFilter({ title, options, selected, onChange, onClearFilter, type = 'checkbox' }: CustomFilterProps) {
	const [search, setSearch] = useState('');

	const filteredOptions = options.filter((option) => option.label.toLowerCase().includes(search.toLowerCase()));

	const displayControls = options.length > 5;

	const handleDateSelect = (date: Date | undefined) => {
		onChange({
			...selected,
			dateValue: date
		});
	};

	const formatDateForDisplay = (date: Date) => {
		return format(date, 'dd/MM/yyyy');
	};

	const renderContent = () => {
		if (type === 'date' || (type === 'mixed' && selected.dateValue)) {
			return (
				<div className='p-2'>
					<CalendarComponent mode='single' selected={selected.dateValue} onSelect={handleDateSelect} />
				</div>
			);
		}

		return (
			<div className='max-h-[300px] overflow-auto p-2'>
				{filteredOptions.map((option) => (
					<label
						key={option.value.toString()}
						className='flex cursor-pointer items-center space-x-2 rounded-md p-2 hover:bg-muted'
						htmlFor={`filter-${option.label}`}>
						<Checkbox
							id={`filter-${option.label}`}
							checked={selected.checkboxValues.includes(option.value.toString())}
							onCheckedChange={(checked) => {
								if (checked) {
									onChange({
										...selected,
										checkboxValues: [...selected.checkboxValues, option.value.toString()]
									});
								} else {
									onChange({
										...selected,
										checkboxValues: selected.checkboxValues.filter((value) => value !== option.value.toString())
									});
								}
							}}
						/>
						<span className='flex-1'>{capitalizeCase(option.label)}</span>
						{option.icon}
						<span className='text-muted-foreground'>{option.count}</span>
					</label>
				))}
			</div>
		);
	};

	return (
		<div className='flex items-center gap-2'>
			<DropdownMenu>
				<Button variant='neutral' size='sm' className='gap-2 bg-card rounded-full cursor-default'>
					<div className='flex relative gap-1.5'>
						<DropdownMenuTrigger asChild>
							<div className='flex gap-1 cursor-pointer items-center'>
								{type === 'date' ? <Calendar className='h-4 w-4' /> : <Plus className='h-4 w-4' />}
								{title}
							</div>
						</DropdownMenuTrigger>

						{onClearFilter ? (
							<>
								{selected.dateValue && (
									<Badge
										variant='secondary'
										className='rounded-full pr-1 cursor-pointer'
										onClick={() => onClearFilter(selected.dateValue as Date)}>
										{formatDateForDisplay(selected.dateValue)}
										<X className='h-3 w-3 ml-1' />
									</Badge>
								)}
								{selected.checkboxValues.map((value) => {
									const option = options.find((opt) => opt.value === value);
									return (
										<Badge
											key={value}
											variant='secondary'
											className='rounded-full pr-1 cursor-pointer'
											onClick={() => onClearFilter(value)}>
											{capitalizeCase(option?.label as string)}
											<X className='h-3 w-3 ml-1' />
										</Badge>
									);
								})}
							</>
						) : (
							<>
								{selected.checkboxValues.length !== 0 && (
									<div className='absolute flex justify-center items-center  bg-blue-600  w-4 h-4 -right-5 -top-2 rounded-3xl text-xs text-white'>
										{selected.checkboxValues.length}
									</div>
								)}
								{selected.dateValue && (
									<div className='absolute flex justify-center items-center  bg-blue-600  w-4 h-4 -right-5 -top-2 rounded-3xl text-xs text-white'>
										1
									</div>
								)}
							</>
						)}
					</div>
				</Button>

				<DropdownMenuContent className='p-0 rounded-2xl -translate-x-3 translate-y-2' align='start'>
					{displayControls && type !== 'date' && (
						<div className='flex items-center border-b p-2'>
							<Search className='ml-2 h-4 w-4 shrink-0 text-muted-foreground' />
							<input
								placeholder='Rechercher un filtre'
								value={search}
								onChange={(e) => setSearch(e.target.value)}
								className='flex h-8 w-full rounded-md bg-transparent py-3 text-sm outline-none border-none focus:ring-0 placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50'
							/>
						</div>
					)}
					{renderContent()}
					{displayControls && (
						<div className='p-1 border-t'>
							<Button
								variant='ghost'
								size='sm'
								className='w-full justify-center gap-2 text-muted-foreground rounded-b-xl rounded-t-sm'
								onClick={() => onChange({ checkboxValues: [], dateValue: undefined })}>
								<X className='h-4 w-4' />
								<span>Effacer ce filtre</span>
							</Button>
						</div>
					)}
				</DropdownMenuContent>
			</DropdownMenu>
		</div>
	);
}
