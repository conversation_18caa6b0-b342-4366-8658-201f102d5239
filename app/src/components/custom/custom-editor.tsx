import { useEffect } from 'react';
import { createSlatePlugin, type Decorate, TextApi } from '@udecode/plate';
import { BasicMarksPlugin } from '@udecode/plate-basic-marks/react';
import { Plate, PlateContent, type TPlateEditor, usePlateEditor } from '@udecode/plate/react';
import { twMerge } from 'tailwind-merge';
import Prism from 'prismjs';
import 'prismjs/components/prism-markdown';
import 'prismjs/themes/prism.css';
import { BasicElementsPlugin } from '@udecode/plate-basic-elements/react';
import { Editor } from 'slate';
import { MarkdownPlugin } from '@udecode/plate-markdown';

// ----------------------------------------------------------------------

export type TDataEditor = {
	value: any;
	valueText: string;
	editor?: TPlateEditor | any;
	isReset: boolean;
};
type Props = {
	className?: string;
	data: TDataEditor;
	onChange: (data: TDataEditor) => void;
};

const getLength = (token: string | Prism.Token): number => {
	if (typeof token === 'string') {
		return token.length;
	}
	if (typeof token.content === 'string') {
		return token.content.length;
	}
	if (Array.isArray(token.content)) {
		return token.content.reduce((l, t) => l + getLength(t), 0);
	}
	if (typeof token.content === 'object' && token.content !== null) {
		return getLength(token.content as Prism.Token);
	}
	return 0;
};

const decoratePreview: Decorate = ({ entry: [node, path] }) => {
	const ranges: any[] = [];
	if (!TextApi.isText(node) || !node.text) {
		return ranges;
	}

	if (!Prism.languages.markdown) {
		return ranges;
	}

	try {
		const tokens = Prism.tokenize(node.text, Prism.languages.markdown);
		let start = 0;

		for (const token of tokens) {
			const length = getLength(token);
			const end = start + length;

			if (typeof token !== 'string') {
				const tokenType = token.type;

				const range = {
					anchor: { offset: start, path },
					focus: { offset: end, path },
					isToken: true,
					tokenType: tokenType
				};

				ranges.push(range);
			}
			start = end;
		}

		const text = node.text;
		const underlineRegex = /__(.*?)__/g;
		let match;

		while ((match = underlineRegex.exec(text)) !== null) {
			const start = match.index;
			const end = start + match[0].length;

			ranges.push({
				anchor: { offset: start, path },
				focus: { offset: end, path },
				isUnderline: true,
				bold: false,
				isToken: false
			});
		}

		const singleUnderscoreRegex = /(?<![\w])_([^_]+)_(?![\w])/g;
		while ((match = singleUnderscoreRegex.exec(text)) !== null) {
			const start = match.index;
			const end = start + match[0].length;

			ranges.push({
				anchor: { offset: start, path },
				focus: { offset: end, path },
				italic: false,
				isToken: false,
				isPlainText: true
			});
		}
	} catch (error) {
		console.error('Error during Prism tokenization:', error);
		return ranges;
	}

	return ranges;
};

export default function CustomEditor({ data, onChange, className }: Props) {
	const editor = usePlateEditor({
		value: data.value,
		plugins: [
			BasicElementsPlugin,
			BasicMarksPlugin,
			MarkdownPlugin,
			createSlatePlugin({
				key: 'preview-markdown',
				decorate: decoratePreview
			})
		]
	});

	useEffect(() => {
		if (data?.isReset && editor?.tf?.reset) {
			editor.tf.reset();
		}
	}, [data, editor]);

	return (
		<Plate
			editor={editor}
			onChange={(newValue) => {
				const valueText = editor.children.map((_block: any, index: any) => Editor.string(editor as any, [index])).join('\n');

				onChange({
					value: newValue,
					valueText,
					editor: editor,
					isReset: false
				});
			}}>
			<div className='flex w-full flex-col'>
				<PlateContent
					placeholder='Insérez votre message ici'
					className={twMerge(
						'language-markdown prism max-h-50 min-h-8 w-full bg-background px-3.75 py-3 outline-0 text-sm',
						className
					)}
					renderLeaf={({ attributes, children, leaf }) => {
						const classNames: string[] = [];

						if (leaf.isUnderline) {
							classNames.push('underline');
						} else {
							if (leaf.bold) classNames.push('font-bold');
							if (leaf.italic) classNames.push('italic');
							if (leaf.strikethrough) classNames.push('line-through');
							if (leaf.code) classNames.push('code');
							if (leaf.underline) classNames.push('underline');

							if (leaf.isToken && leaf.tokenType) {
								classNames.push('token');
								classNames.push(leaf.tokenType as string);
							}
						}

						return (
							<span {...attributes} className={twMerge(classNames)}>
								{children}
							</span>
						);
					}}
				/>
			</div>
		</Plate>
	);
}
