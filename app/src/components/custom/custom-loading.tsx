// utils
import { cn } from '@/lib/utils';
// components
import SimpleAnimatedLogo from '@/components/custom/animated-logo.tsx';

// ----------------------------------------------------------------------

type Props = {
	className?: string;
};
export default function CustomLoading({ className }: Props) {
	return (
		<div className={cn('h-screen w-screen flex justify-center items-center', className)}>
			<SimpleAnimatedLogo />
		</div>
	);
}
