// utils
import { useFormContext } from 'react-hook-form';
// components
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '../ui/select';

// ----------------------------------------------------------------------
export type TSelectItem = {
	label: string;
	value: string | number;
};

type Props = {
	name: string;
	items: TSelectItem[];
	label?: string;
	placeholder?: string;
};

export function RHFSelect({ name, items, label, placeholder }: Props) {
	const { control } = useFormContext();

	return (
		<FormField
			name={name}
			control={control}
			render={({ field }) => (
				<FormItem>
					{label && <FormLabel className='font-normal'>{label}</FormLabel>}
					<Select
						{...field}
						onValueChange={(e) => {
							field.onChange(e);
						}}
						value={field.value}
						required>
						<FormControl>
							<SelectTrigger className='rounded-3xl border-0 bg-input'>
								<SelectValue placeholder={placeholder || ''} />
							</SelectTrigger>
						</FormControl>
						<SelectContent className='rounded-xl'>
							<SelectGroup className='rounded-xl'>
								{items.map((item) => (
									<SelectItem className='rounded-xl' key={item.value} value={item.value.toString()}>
										{item.label.toString()}
									</SelectItem>
								))}
							</SelectGroup>
						</SelectContent>
					</Select>
					<FormMessage />
				</FormItem>
			)}
		/>
	);
}
