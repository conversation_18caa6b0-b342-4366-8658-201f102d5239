// utils
import type { UseFormReturn } from 'react-hook-form';
import { FormProvider } from 'react-hook-form';

// ----------------------------------------------------------------------

export type FormProps = {
	onSubmit?: () => void;
	children: React.ReactNode;
	methods: UseFormReturn<any>;
	className?: string;
};

export function Form({ children, onSubmit, methods, className }: FormProps) {
	return (
		<FormProvider {...methods}>
			<form onSubmit={onSubmit} noValidate autoComplete='off' className={className}>
				{children}
			</form>
		</FormProvider>
	);
}
