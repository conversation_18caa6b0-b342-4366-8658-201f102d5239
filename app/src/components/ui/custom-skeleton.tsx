import { ReactElement } from 'react';
// others
import { cn } from '@/lib/utils';

// ----------------------------------------------------------------------
interface Props {
	shouldDisplay: any | undefined;
	children: ReactElement;
}

function Skeleton({ shouldDisplay, children, className, ...props }: React.HTMLAttributes<HTMLDivElement> & Props) {
	return shouldDisplay ? children : <div className={cn('animate-pulse rounded-md bg-primary/10', className)} {...props} />;
}

export { Skeleton };
