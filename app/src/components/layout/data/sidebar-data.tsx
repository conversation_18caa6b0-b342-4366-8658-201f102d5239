// types
import type { SidebarData } from '../types';

// ----------------------------------------------------------------------

export const sidebarData: SidebarData = {
	/*	teams: [
		{
			name: 'ProTeam',
			logo: <ProUILogo from='#671E75' to='#D24036' size={36} />
		},
		{
			name: 'Expertise Client',
			logo: <ProUILogo from='#8E2DE2' to='#5407E1' size={36} />
		},
		{
			name: 'Conduite d’Activité',
			logo: <ProUILogo from='#F12711' to='#F5AF19' size={36} />
		},
		{
			name: 'Bureau d’Étude',
			logo: <ProUILogo from='#30B491' to='#8EF519' size={36} />
		},
		{
			name: 'Développement R&D',
			logo: <ProUILogo from='#BF2F72' to='#FF86DB' size={36} />
		},
		{
			name: 'Collaborateur',
			logo: <ProUILogo from='#494949' to='#BCBCBC' size={36} />
		}
	],*/
	navItems: [
		{
			title: 'Tableau de bord',
			url: '/dashboard',
			icon: 'Home04'
		},
		// {
		// 	title: 'État du parc',
		// 	url: '',
		// 	icon: 'Building02'
		// },
		{
			title: 'Clients',
			icon: 'Users01',
			items: [
				{
					title: 'Entreprises',
					url: '/clients/companies'
				},
				{
					title: 'Devis',
					url: '/clients/quotes'
				},
				{
					title: 'Commandes',
					url: '/clients/orders'
				},
				{
					title: 'Facturation',
					url: '/clients/invoices'
				}
			]
		},
		{
			title: 'Fibre',
			icon: 'Signal03',
			items: [
				{
					title: 'FTTH',
					url: '/fiber/orders',
					search: { type: 'ftth' }
				},
				{
					title: 'FTTO',
					url: '/fiber/orders',
					search: { type: 'ftto' }
				},
				{
					title: 'Blocages',
					url: '/fiber/blocks'
				},
				{
					title: 'Éligibilité',
					url: '/fiber/eligibility'
				},
				{
					title: 'Gestion du parc',
					url: '/fiber/park-management'
				},
				{
					title: 'Interventions',
					url: '/fiber/interventions'
				}
			]
		},
		{
			title: 'Mobile',
			icon: 'Phone01',
			items: [
				{
					title: 'Mobile Pro',
					url: '/mobile/pro'
				},
				// {
				// 	title: 'Grandes Flottes',
				// 	url: '/mobile/fleets'
				// },
				{
					title: 'Terminaux',
					url: '/mobile/terminals'
				},
				{
					title: 'Portabilités',
					url: '/mobile/portability'
				},
				{
					title: 'SIM & eSIM',
					url: '/mobile/sim'
				}
			]
		},
		{
			title: 'Tickets',
			icon: 'Ticket01',
			items: [
				{
					title: 'Tickets FTTH',
					url: '/tickets/fiber',
					search: { type: 'ftth' }
				},
				// {
				// 	title: 'Tickets FTTO',
				// 	url: '/tickets/fiber',
				// 	search: { type: 'ftto' }
				// },
				{
					title: 'Tickets mobiles Pro',
					url: '/tickets/mobile'
				}
				// {
				// 	title: 'Tickets Grandes Flottes',
				// 	url: '/tickets/fleets'
				// }
			]
		}
	]
};
