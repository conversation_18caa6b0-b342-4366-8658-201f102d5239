import type { ReactNode } from 'react';
// tanstack
import { <PERSON> } from '@tanstack/react-router';
// utils
import { ChevronLeft } from '@tools/reactor-icons';
import { Typography } from '@tools/reactore';
// others
import { cn } from '@/lib/utils.ts';

// ----------------------------------------------------------------------

type Props = {
	title: string | ReactNode;
	href?: string;
	className?: string;
	children?: ReactNode;
};

export default function AccountHeader({ children, title, href, className }: Props) {
	return (
		<header className={cn('mb-4 flex h-8 justify-between', className)}>
			<div className='flex items-center'>
				{href && (
					<Link to={href} className='mr-4'>
						<ChevronLeft className='w-8' />
					</Link>
				)}
				{typeof title === 'string' && <Typography className='font-iliad font-normal text-2xl'>{title}</Typography>}
				{typeof title === 'object' && title}
			</div>
			<div className='flex items-center gap-x-10'>{children}</div>
		</header>
	);
}
