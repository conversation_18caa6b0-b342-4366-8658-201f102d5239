<svg width="739" height="584" viewBox="0 0 739 584" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_1136_58105)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M230.664 4.90127e-05C263.12 -0.0522878 279.106 41.8248 308.552 55.8467C349.896 75.534 418.021 54.3796 435.577 97.5779C452.572 139.399 402.288 179.854 372.553 213.332C352.169 236.281 321.784 242.779 294.571 256.332C273.249 266.951 254.358 283.633 230.664 283.995C206.772 284.361 188.821 262.957 165.359 258.307C112.497 247.83 25.1531 292.718 8.80767 240.023C-7.58466 187.178 92.0093 174.197 125.868 130.995C142.534 109.729 136.079 77.1897 152.953 56.0976C173.559 30.3407 198.21 0.052383 230.664 4.90127e-05Z" fill="url(#paint0_linear_1136_58105)" fill-opacity="0.3"/>
</g>
<defs>
<filter id="filter0_f_1136_58105" x="-293" y="-300" width="1032" height="884" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="150" result="effect1_foregroundBlur_1136_58105"/>
</filter>
<linearGradient id="paint0_linear_1136_58105" x1="223" y1="0" x2="223" y2="284" gradientUnits="userSpaceOnUse">
<stop stop-color="#4C68CE"/>
<stop offset="1" stop-color="#EC7272"/>
</linearGradient>
</defs>
</svg>
