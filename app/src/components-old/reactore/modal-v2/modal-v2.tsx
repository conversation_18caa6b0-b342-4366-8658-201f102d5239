// utils
import { InPortal } from '@tools/reactore';
import { twJoin } from 'tailwind-merge';

// ----------------------------------------------------------------------

type TPositionX = 'right' | 'left' | 'center' | undefined;
type TPositionY = 'bottom' | 'top' | 'center' | 'midTop' | undefined;

type TAnimate = 'spawnByDown' | undefined;

type Props = {
	isOpen: boolean;
	onClose: () => void;
	children: React.ReactNode;
	className?: string;
	classNameBg?: string;
	animate?: TAnimate;
	positionX?: TPositionX;
	positionY?: TPositionY;
	notMinH?: boolean;
};

export default function ModalV2({
	isOpen,
	onClose,
	children,
	className,
	animate,
	classNameBg,
	positionX = 'center',
	positionY = 'midTop',
	notMinH = false
}: Props) {
	const handleAnimate = () => {
		switch (animate) {
			case 'spawnByDown':
				return 'animate-opacity_spawn opacity-0';
			default:
				return '';
		}
	};

	const handlePositionX = () => {
		switch (positionX) {
			case 'right':
				return 'justify-end';
			case 'left':
				return 'justify-start';
			case 'center':
				return 'justify-center';
			default:
				return '';
		}
	};

	const handlePositionY = () => {
		switch (positionY) {
			case 'bottom':
				return 'items-end';
			case 'top':
				return 'items-start';
			case 'center':
				return 'items-center';
			case 'midTop':
				return 'pt-[9%]';
			default:
				return '';
		}
	};

	return (
		<InPortal>
			{isOpen && (
				<InPortal>
					<div
						tabIndex={0}
						onClick={(event) => {
							if (event.target === event.currentTarget) {
								onClose();
							}
						}}
						onKeyDown={(event) => {
							if (event.key === 'Enter' || event.key === ' ') {
								onClose();
							}
						}}
						role='button'
						aria-hidden='true'
						className={twJoin(
							'b-solid fixed bottom-0 left-0 right-0 top-0 flex border-free-gray-200 bg-white/5 backdrop-blur-[6px]',
							handleAnimate(),
							classNameBg && classNameBg,
							handlePositionX(),
							handlePositionY()
						)}>
						<div className={twJoin('h-full w-full', !notMinH && 'h-min', className && className)}>
							<div className={twJoin('h-full w-full rounded-xl bg-white shadow-xl shadow-slate-700/10')}>{children}</div>
						</div>
					</div>
				</InPortal>
			)}
		</InPortal>
	);
}
