// utils
import { twJoin } from 'tailwind-merge';
// others
import type { TAutoCompleteItem, TAutoCompletion } from '../Input.interface';

// ----------------------------------------------------------------------

type Props = {
	items: TAutoCompleteItem[];
	autoCompletion: TAutoCompletion;
	queryAutoComplete: string;
	refInput: React.MutableRefObject<HTMLInputElement | undefined>;
	setIsShowItemsAutoComplete: (value: boolean) => void;
};

export default function AutoCompleteList({ items, queryAutoComplete, autoCompletion, refInput, setIsShowItemsAutoComplete }: Props) {
	const regexFilter = new RegExp(`^${queryAutoComplete}`, 'i');
	const regexFilterStrPost = new RegExp(`${queryAutoComplete}`, 'i');

	const filteredItems = items
		.sort((a, b) => {
			if (a.label > b.label) {
				return 1;
			}
			return -1;
		})
		.slice(0, autoCompletion?.maxResults ? autoCompletion?.maxResults : 5)
		.filter((item: TAutoCompleteItem) => {
			return regexFilter.test(item.label) || regexFilterStrPost.test(item.label);
		});

	if (items?.length === 0)
		return (
			<div className='w-full py-2 text-center'>{autoCompletion?.loading ? 'En cours de chargement...' : 'Aucune correspondance'}</div>
		);

	return (
		<>
			{filteredItems.map((item, index) => {
				return (
					<div
						key={item.value}
						data-test-id={`option-address-${index}`}
						className={twJoin(
							'"w-full dark:hover:bg-free-dark-800 h-10 px-2 py-2 hover:bg-gray-100 md:px-5',
							item.value === autoCompletion?.itemSelected && 'bg-gray-200'
						)}
						role='button'
						tabIndex={0}
						onMouseDown={(e) => {
							e.stopPropagation();
							if (refInput?.current) refInput.current.value = item.label;
							if (autoCompletion?.onSelectItem) {
								autoCompletion.onSelectItem({ value: item.value, label: item.label }, refInput.current);
							}
							setIsShowItemsAutoComplete(false);
						}}>
						{item.label}
					</div>
				);
			})}
		</>
	);
}
