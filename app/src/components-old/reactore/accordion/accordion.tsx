import { useState } from 'react';
// components
import AccordionContext from './accordion-context';
import type { TAccordionContext } from './accordion-context/accordion-context';

// ----------------------------------------------------------------------

type Props = {
	open?: boolean;
	onClick?: () => void;
	children?: React.ReactNode;
	className?: string;
};

export default function Accordion({ children, className, open, onClick }: Props) {
	const [openAccordion, setOpenAccordion] = useState<boolean>(false);

	const provider: TAccordionContext =
		typeof open === 'boolean' && onClick
			? { open, onClick }
			: { open: openAccordion, onClick: () => setOpenAccordion((prev) => !prev) };

	return (
		<AccordionContext.Provider value={provider}>
			<div className={className}>{children}</div>
		</AccordionContext.Provider>
	);
}
