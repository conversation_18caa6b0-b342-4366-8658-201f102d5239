// utils
import * as Sentry from '@sentry/react';
// constants
import { globalEnv, PROJECT_VERSION } from '@/env';
// others
import { router } from './services/router.service';

// ----------------------------------------------------------------------

const tracePropagationTargets = ['localhost', /.*\.(iliad|free)\.fr.*/];

globalEnv.loadEnv(window);

Sentry.init({
	enabled: globalEnv.env.SENTRY_STATE === 'enabled',
	dsn: `https://<EMAIL>/5`,
	release: `${PROJECT_VERSION}`,
	environment: globalEnv.env.APP_ENV,
	initialScope: (scope) => {
		console.debug('[sentry] Load...');

		scope.setTags({ stack_name: 'b2b', stack_scope: 'front' });

		return scope;
	},
	tracesSampleRate: 1.0,
	sampleRate: 1.0,
	profilesSampleRate: 1.0,
	replaysOnErrorSampleRate: 1.0,
	replaysSessionSampleRate: 0.5,
	integrations: [
		Sentry.tanstackRouterBrowserTracingIntegration(router),
		Sentry.replayIntegration({
			maskAllText: false,
			maskAllInputs: true,
			blockAllMedia: true
		}),
		Sentry.browserProfilingIntegration(),
		Sentry.reportingObserverIntegration()
	],
	tracePropagationTargets
});
