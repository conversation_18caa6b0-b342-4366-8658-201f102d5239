// api
import { getEnumItems } from '../utils';
// types
import type { IApiEnum } from '../types';

// ----------------------------------------------------------------------

export enum ETicketTransition {
	WAIT = 'wait',
	TO_BE_CORRECT = 'to_be_correct',
	OPEN = 'open',
	DELETE = 'delete',
	ANSWER = 'answer',
	AWAIT = 'await',
	CLOSE = 'close',
	REOPEN = 'reopen',
	SOLVE = 'solve',
	REJECT = 'reject'
}
export const ETicketTransitionId = [
	'/ticket_transition_enums/CREATED',
	'/ticket_transition_enums/WAITING',
	'/ticket_transition_enums/CORRECTED',
	'/ticket_transition_enums/OPENED',
	'/ticket_transition_enums/AWAITING_CLOSURE',
	'/ticket_transition_enums/CLOSED',
	'/ticket_transition_enums/DELETED',
	'/ticket_transition_enums/SOLVED'
] as const;

export type TTicketTransition = Lowercase<keyof typeof ETicketTransition>;

export const TICKET_TRANSITION_OPTIONS: IApiEnum<TTicketTransition>[] = [
	{
		'@id': '/ticket_transition_enums/WAIT',
		'@type': 'TicketTransitionEnum',
		key: 'WAIT',
		value: ETicketTransition.WAIT,
		label: 'crée'
	},
	{
		'@id': '/ticket_transition_enums/TO_BE_CORRECT',
		'@type': 'TicketTransitionEnum',
		key: 'TO_BE_CORRECT',
		value: ETicketTransition.TO_BE_CORRECT,
		label: 'en attente'
	},
	{
		'@id': '/ticket_transition_enums/OPEN',
		'@type': 'TicketTransitionEnum',
		key: 'OPEN',
		value: ETicketTransition.OPEN,
		label: 'en cours de correction'
	},
	{
		'@id': '/ticket_transition_enums/DELETE',
		'@type': 'TicketTransitionEnum',
		key: 'DELETE',
		value: ETicketTransition.DELETE,
		label: 'ouvert'
	},
	{
		'@id': '/ticket_transition_enums/ANSWER',
		'@type': 'TicketTransitionEnum',
		key: 'ANSWER',
		value: ETicketTransition.ANSWER,
		label: 'en attente de fermeture'
	},
	{
		'@id': '/ticket_transition_enums/AWAIT',
		'@type': 'TicketTransitionEnum',
		key: 'AWAIT',
		value: ETicketTransition.AWAIT,
		label: 'fermé'
	},
	{
		'@id': '/ticket_transition_enums/CLOSE',
		'@type': 'TicketTransitionEnum',
		key: 'CLOSE',
		value: ETicketTransition.CLOSE,
		label: 'supprimé'
	},
	{
		'@id': '/ticket_transition_enums/REOPEN',
		'@type': 'TicketTransitionEnum',
		key: 'REOPEN',
		value: ETicketTransition.REOPEN,
		label: 'résolu'
	},
	{
		'@id': '/ticket_transition_enums/SOLVE',
		'@type': 'TicketTransitionEnum',
		key: 'SOLVE',
		value: ETicketTransition.SOLVE,
		label: 'résolu'
	},
	{
		'@id': '/ticket_transition_enums/REJECT',
		'@type': 'TicketTransitionEnum',
		key: 'REJECT',
		value: ETicketTransition.REJECT,
		label: 'résolu'
	}
];

export const TICKET_TRANSITION_ITEMS = getEnumItems(TICKET_TRANSITION_OPTIONS);
export const getTicketTransitionLabel = (value?: string) => {
	const ticketTransitionOption = TICKET_TRANSITION_OPTIONS.find((option) => option.value === value);
	return ticketTransitionOption ? ticketTransitionOption.label : '';
};
export const getTicketTransitionLabelById = (value?: string) => {
	const ticketTransitionOption = TICKET_TRANSITION_OPTIONS.find((option) => option['@id'] === value);
	return ticketTransitionOption ? ticketTransitionOption.label : '';
};
export const getTicketTransitionValueById = (value?: string): TTicketTransition | undefined => {
	const ticketTransitionOption = TICKET_TRANSITION_OPTIONS.find((option) => option['@id'] === value);
	return ticketTransitionOption?.value;
};
export const getTicketTransitionIdByValue = (value?: string): string | undefined => {
	const ticketTransitionOption = TICKET_TRANSITION_OPTIONS.find((option) => option.value === value);
	return ticketTransitionOption?.['@id'];
};

// TODO: to dev type and logic generic
// export const getTicketTransitionBy = (value?: string, valueKey): TTicketTransition | undefined => {
// 	const ticketTransitionOption = TICKET_TRANSITION_OPTIONS.find((option) => option['@id'] === value)
// 	return ticketTransitionOption?.value
// }
