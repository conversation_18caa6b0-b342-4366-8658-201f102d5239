// types
import type { IA<PERSON>E<PERSON> } from '../types';

// ----------------------------------------------------------------------

export enum JobTypeEnum {
	COMMERCIAL = 'commercial',
	MARKETING = 'marketing',
	RH = 'rh',
	SUPPORT = 'support',
	TECH = 'tech',
	OTHER = 'other'
}

export type JobType = (typeof JobTypeEnum)[keyof typeof JobTypeEnum];

export const JOB_TYPE_OPTIONS = (): IApiEnum[] => [
	{
		'@id': '/job_type_enums/COMMERCIAL',
		'@type': 'JobTypeEnum',
		key: 'COMMERCIAL',
		value: JobTypeEnum.COMMERCIAL,
		label: 'Commercial'
		// label: t('jobTypeEnum.commercial', { ns: nameSpaces.enums }) t('jobTypeEnum.commercial', { ns: nameSpaces.enums })
	},
	{
		'@id': '/job_type_enums/MARKETING',
		'@type': 'JobTypeEnum',
		key: 'MARKETING',
		value: JobTypeEnum.MARKETING,
		label: 'Marketing'
		// label: t('jobTypeEnum.marketing', { ns: nameSpaces.enums }) t('jobTypeEnum.marketing', { ns: nameSpaces.enums })
	},
	{
		'@id': '/job_type_enums/RH',
		'@type': 'JobTypeEnum',
		key: 'RH',
		value: JobTypeEnum.RH,
		label: 'RH'
		// label: t('jobTypeEnum.rh', { ns: nameSpaces.enums }) t('jobTypeEnum.rh', { ns: nameSpaces.enums })
	},
	{
		'@id': '/job_type_enums/SUPPORT',
		'@type': 'JobTypeEnum',
		key: 'SUPPORT',
		value: JobTypeEnum.SUPPORT,
		label: 'Support'
		// label: t('jobTypeEnum.support', { ns: nameSpaces.enums }) t('jobTypeEnum.support', { ns: nameSpaces.enums })
	},
	{
		'@id': '/job_type_enums/TECH',
		'@type': 'JobTypeEnum',
		key: 'TECH',
		value: JobTypeEnum.TECH,
		label: 'Tech'
		// label: t('jobTypeEnum.tech', { ns: nameSpaces.enums }) t('jobTypeEnum.tech', { ns: nameSpaces.enums })
	},
	{
		'@id': '/job_type_enums/OTHER',
		'@type': 'JobTypeEnum',
		key: 'OTHER',
		value: JobTypeEnum.OTHER,
		label: 'Other'
		// label: t('jobTypeEnum.other', { ns: nameSpaces.enums }) t('jobTypeEnum.other', { ns: nameSpaces.enums })
	}
];

export const getJobTypeValue = (type: JobTypeEnum | null): string => {
	const jobTypeValue = JOB_TYPE_OPTIONS().find((jobType) => jobType['@id'] === type);
	return jobTypeValue ? jobTypeValue.value : '';
};
