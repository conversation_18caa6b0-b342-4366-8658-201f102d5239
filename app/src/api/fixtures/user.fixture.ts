// constants
import { VITE_MOCKING } from '@/env';

// ----------------------------------------------------------------------

const loginPublicEnv = import.meta.env.VITE_LOGIN ? JSON.parse(import.meta.env.VITE_LOGIN as string) : undefined;

/*
login fiber
------------
email: 'free'
password: 'nKokn626'
------------------------------
------------------------------
login magnet
-----------
email: 'mlauriotditprevost'
password: 'azerty123'

*/

// const loginAudil = {
// 	email: 'mlauriotditprevost',
// 	password: 'azerty123'
// }

const loginMsw = {
	email: 'staff ',
	password: 'azerty123'
};
const loginFx = {
	password: 'nKokn626'
};

const loginDefault = VITE_MOCKING ? loginMsw : loginFx;
/**
 * @example
 * email: 'mlauriotditprevost',
 * password: 'azerty123'
 *
 * @example
 * email: 'free',
 * password: 'azerty123'
 *
 * @type {{password: string, email: string}}
 */
export const login = loginPublicEnv ? { ...loginPublicEnv } : loginDefault;
