// utils
import * as Sentry from '@sentry/react';
// api
import Fetcher from './fetcher-service';
// contexts
import { getSession } from '@/auth/utils';
// constants
import { globalEnv } from '@/env';
// types
import type { ApiArg } from '../types';

// ----------------------------------------------------------------------

const fetcher = new Fetcher({
	config: {
		// headers: {
		// 	'PP-auth': 'NP6uZJxFXKNG8HGqwoDqx5t5fSZozPy2'
		// },
		errorInterceptor: (error) => {
			const { data, status, config, statusText } = error?.response || {};
			const { url: sourceRequest } = config;

			const excludedRoutes = ['logout', 'login', 'password-create', 'password-update', 'password-reset'];

			const errorMessages = ['Request failed with status code 401', 'JWT Token not found', 'Expired JWT Token', 'Invalid JWT Token'];

			const isRedirectLogout = errorMessages.some((message) => data?.message === message);
			const isExcludeRoute = excludedRoutes.some((route) => window.location.href.includes(route));
			const isExcludeRouteConfig = excludedRoutes.some((route) => sourceRequest?.includes(route));

			if (!isExcludeRoute && !isExcludeRouteConfig && sourceRequest !== '/me') {
				if (data && status === 401 && isRedirectLogout) {
					const { pathname, search } = window.location;
					const redirectTo = `${pathname}${search}`;

					if (excludedRoutes.every((excludedUrl) => !pathname?.includes(excludedUrl))) {
						const redirectToEncoded = encodeURIComponent(redirectTo);

						window.location.href = `/logout?redirectTo=${redirectToEncoded}`;
					}
				}
			}

			if (status && status <= 500 && status !== 401 && status !== 403 && status !== 404) {
				const dataSentry = {
					// @ts-ignore: te review
					code: error?.code,
					url: config.url,
					message: [data.message, error.message, statusText],
					status,
					method: config?.method
				};

				Sentry.startSpan(
					{
						name: 'Fetcher services errorInterceptor',
						op: 'http',
						attributes: dataSentry
					},
					(span) => {
						if (span) {
							span.setStatus(Sentry.getSpanStatusFromHttpCode(status));
						}

						Sentry.captureMessage(data?.message || error?.message || 'error message a/n', (scope) => {
							scope.setLevel('warning');
							scope.setTransactionName('Interceptor error');
							return scope;
						});
					}
				);
			}

			return error;
		}
	}
});

export const fetcherFn = <T>({ url, params, config, data, method = 'get' }: ApiArg): Promise<T> => {
	const accessToken = getSession();

	return fetcher[method]({
		url,
		params,
		data,
		config: {
			...config,
			headers: {
				Authorization: accessToken ? `Bearer ${accessToken}` : undefined,
				'Content-Type': 'application/ld+json',
				Accept: 'application/ld+json',
				...config?.headers
			}
		}
	});
};

// ----------------------------------------------------------------------

export const cellFetcher = <T>(args: ApiArg): Promise<T> => {
	return fetcherFn<T>({
		...args,
		config: {
			...args.config,
			baseURL: globalEnv.env.PUBLIC_CELL_API_URL
		}
	});
};

export const deliverFetcher = <T>(args: ApiArg): Promise<T> => {
	return fetcherFn<T>({
		...args,
		config: {
			...args.config,
			baseURL: globalEnv.env.PUBLIC_DELIVER_API_URL
		}
	});
};

export const fiberFetcher = <T>(args: ApiArg): Promise<T> => {
	return fetcherFn<T>({
		...args,
		config: {
			...args.config,
			baseURL: globalEnv.env.PUBLIC_FIBER_API_URL
		}
	});
};

export const magnetFetcher = <T>(args: ApiArg): Promise<T> => {
	return fetcherFn<T>({
		...args,
		config: {
			...args.config,
			baseURL: globalEnv.env.PUBLIC_MAGNET_API_URL
		}
	});
};

export const meteorFetcher = <T>(args: ApiArg): Promise<T> => {
	return fetcherFn<T>({
		...args,
		config: {
			...args.config,
			baseURL: globalEnv.env.PUBLIC_METEOR_API_URL
		}
	});
};

export const pawnFetcher = <T>(args: ApiArg): Promise<T> => {
	return fetcherFn<T>({
		...args,
		config: {
			...args.config,
			baseURL: globalEnv.env.PUBLIC_PAWN_API_URL
		}
	});
};

export const crossroadFetcher = <T>(args: ApiArg): Promise<T> => {
	return fetcherFn<T>({
		...args,
		config: {
			...args.config,
			baseURL: globalEnv.env.PUBLIC_CROSSROAD_API_URL
		}
	});
};

export const piggyFetcher = <T>(args: ApiArg): Promise<T> => {
	return fetcherFn<T>({
		...args,
		config: {
			...args.config,
			baseURL: globalEnv.env.PUBLIC_PIGGY_API_URL
		}
	});
};

// ----------------------------------------------------------------------

export default fetcher;
