// others
import type { IFiberCompanyContact } from './company-contact.interface';
import type { IFiberStaff } from './staff.interface';

// ----------------------------------------------------------------------

export interface IFiberCommunication {
	'@id'?: string;
	'@type'?: string;
	readonly type?: string;
	readonly contact?: IFiberCompanyContact;
	readonly contactedAt?: Date;
	readonly comment?: string;
	readonly appointmentSchedule?: string;
	readonly isSuccess?: boolean;
	readonly isReachable?: boolean;
	readonly reason?: TFiberCommunicationReason;
	readonly direction?: 'inbound' | 'outbound';
	readonly order?: string;
	readonly staff?: IFiberStaff | string;
	readonly failureType?: TFiberFailureType;
	readonly createdAt?: Date;
	readonly updatedAt?: Date;
}

export enum EFiberCommunicationReason {
	new_appointment = 'Nouveau rendez-vous',
	edit_appointment = 'Modification de rendez-vous',
	invoice = 'Facturation',
	need_info = 'Demande de renseignements',
	feedback = 'Feedback'
}

export type TFiberCommunicationReason = keyof typeof EFiberCommunicationReason;
export enum EFiberFailureType {
	no_answer,
	unreachable,
	message_left,
	callback_day,
	callback_week,
	callback_month,
	callback_later,
	order_canceled,
	waiting_invoice,
	bad_number,
	no_planning,
	technical_difficulties,
	other
}

export type TFiberFailureType = keyof typeof EFiberFailureType;
