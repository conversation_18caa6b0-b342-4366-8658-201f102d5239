export interface IFiberTelemetry {
	'@id'?: string;
	order: string;
	data?: string[];
	done: boolean;
	readonly type?: 'rx' | 'tx';
	readonly month?: Date;
	readonly createdAt?: Date;
	readonly updatedAt?: Date;
	readonly last?: number;
	readonly average?: number;
}
type TRxTx = [string, number][];
export interface IFiberTelemetryHistory {
	provisioningPON: boolean;
	tx: TRxTx;
	rx: TRxTx;
	lastUpdateONU: [string, number];
	link: boolean;
}
