export type TFiberCompanyContactType = 'legal_person' | 'planning_manager' | 'delivery_contact' | 'project_manager' | 'support_contact';

export interface IFiberCompanyContact {
	'@id'?: string;
	'@type'?: string;
	type: TFiberCompanyContactType;
	firstName: string;
	lastName: string;
	email?: string;
	phone?: string;
	phone2?: string;
	comment?: string;
	readonly order?: string;
	readonly createdAt?: Date;
	readonly updatedAt?: Date;
}
