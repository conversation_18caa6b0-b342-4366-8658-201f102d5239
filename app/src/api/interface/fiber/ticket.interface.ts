export interface IFiberTicket {
	'@id'?: string;
	cycle: 'pre production' | 'production' | 'post production' | 'SAV';
	comment?: string;
	reason?: string;
	readonly order?: string;
	readonly createdBy?: string;
	readonly ticketMessages?: string[];
	readonly ticketEvents?: string[];
	readonly team?: string;
	readonly state?: 'CREATED' | 'OPENED' | 'CLOSED' | 'SOLVED';
	readonly ticketFavorites?: any;
	readonly createdAt?: Date;
	readonly updatedAt?: Date;
	readonly deletedAt?: Date;
}
