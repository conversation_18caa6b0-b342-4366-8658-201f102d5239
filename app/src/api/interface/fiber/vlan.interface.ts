export type TFiberVlanState = 'CREATED' | 'DEFINED' | 'SENT' | 'ACTIVATED' | 'CANCELLED' | 'TERMINATED';

export interface IFiberVLAN {
	'@id'?: string;
	'@type'?: string;
	provider:
		| 'th2_1'
		| 'mars_1'
		| 'mars_2'
		| 'mars_jag1'
		| 'mars_jag_1'
		| 'jag_anycast_1'
		| 'jag_par01_1'
		| 'jag_par02_1'
		| 'jag_lyo03_1'
		| 'jag_mar02_1'
		| 'jag_bdx01_1'
		| 'jag_nan03_1';
	cvlan: number;
	svlan: number;
	uvlan: number;
	strip: boolean;
	network?: any;
	order: string;
	state: TFiberVlanState;
	readonly createdAt?: Date;
	readonly updatedAt?: Date;
	readonly deletedAt?: Date;
}
