// utils
import type { <PERSON>ormField } from '..';
// api
import type { HydraMember, HydraResponse } from '@/api/types';

// ----------------------------------------------------------------------

export type IForms = HydraResponse<IForm>;

export interface IForm extends HydraMember {
	subject: string;
	treeNode: string;
	name: string;
	col: number | string;
	domain: string;
	formFields: IFormField[];
	formBlocks: string[];
	reference: string;
	team: string;
	route: string[];
}
