// api
import type { <PERSON>orm<PERSON>ield } from '@/api/interface';
import type { HydraMember, HydraResponse } from '@/api/types';

// ----------------------------------------------------------------------

export type IFormFieldConditions = HydraResponse<IFormFieldCondition>;

export interface IFormFieldCondition extends HydraMember {
	reference: string;
	type: string;
	value: string;
	formFieldParent: IFormField;
	formFieldChilds: IFormField[];
}

// FORMS
// ----------------------------------------------------------------------

export interface IFormFieldConditionForm {
	reference: string;
	type: string;
	value: string;
	formFieldParent: string;
	formFieldChilds: string[];
}
