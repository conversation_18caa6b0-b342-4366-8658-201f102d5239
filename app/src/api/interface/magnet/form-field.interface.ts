// utils
import type { IFormFieldCondition } from '..';
// api
import type { TFormFieldTypeFrontEnum } from '@/api/enums/magnet/form-field-type-front.enum.ts';
import type { TFormFieldTypeEnum } from '@/api/enums/magnet/form-field-type.enum.ts';
import type { HydraMember, HydraResponse } from '@/api/types';

// ----------------------------------------------------------------------

export type IFormFields = HydraResponse<IFormField>;

export interface IFormField extends HydraMember {
	name: string;
	formBlock?: string;
	type: TFormFieldTypeEnum;
	inputType: TFormFieldTypeFrontEnum;
	label: string;
	validation?: string;
	col: number | string;
	enum?: string[];
	required: boolean;
	form: string;
	position: string;
	formFieldConditions: IFormFieldCondition[];
	formFieldConditionDepends: IFormFieldCondition[];
	colSpan?: number;
}

// FORMS
// ----------------------------------------------------------------------

export interface IFormFieldForm {
	name: string;
	type: TFormFieldTypeEnum;
	inputType: TFormFieldTypeFrontEnum;
	label: string;
	validation?: string;
	enum?: string[];
	required: boolean;
	form: string;
	position: string;
	colSpan?: number;
}
