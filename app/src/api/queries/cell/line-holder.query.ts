// utils
import { createQuery<PERSON>eys } from '@lukemorales/query-key-factory';
import { parseIri } from '@/api/fetcher';
// api
import type { ICellLineHolder } from '@/api/interface/cell';
import { cellFetcher } from '@/api/fetcher';
import type { HydraResponse } from '@/types';

// ----------------------------------------------------------------------

export const cellLineHolderQueryKeys = createQueryKeys('cell_lineHolder', {
	all: () => ({
		queryKey: ['all'],
		queryFn: () => cellFetcher<HydraResponse<ICellLineHolder>>({ url: 'line_holders' })
	}),
	byId: (id?: string) => {
		const lineHolderId = id ? (parseIri(id) as string) : '';

		return {
			queryKey: [lineHolderId || ''],
			queryFn: () => cellFetcher<ICellLineHolder>({ url: `line_holders/${lineHolderId}` || '' })
		};
	}
});
