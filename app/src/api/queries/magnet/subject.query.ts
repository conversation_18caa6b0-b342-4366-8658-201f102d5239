// utils
import { createQueryKeys } from '@lukemorales/query-key-factory';
import { parseIri } from '@/api/fetcher';
// api
import type { IMagnetSubject } from '@/api/interface/magnet';
import { magnetFetcher } from '@/api/fetcher';
import type { HydraResponse } from '@/types';

// ----------------------------------------------------------------------

export const magnetSubjectQueryKeys = createQueryKeys('subject', {
	all: (params?: any) => {
		return {
			queryKey: [params],
			queryFn: () => magnetFetcher<HydraResponse<IMagnetSubject>>({ url: `/subjects`, params })
		};
	},
	byId: (id?: string) => {
		const subjectId = id && typeof id !== 'object' ? parseIri(id) : '';

		return {
			queryKey: [subjectId || ''],
			queryFn: () => magnetFetcher<IMagnetSubject>({ url: `/subjects/${subjectId}` })
		};
	}
});
