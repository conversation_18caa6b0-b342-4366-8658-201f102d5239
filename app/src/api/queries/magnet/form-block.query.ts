// utils
import { createQuery<PERSON>eys } from '@lukemorales/query-key-factory';
import { parseIri } from '@/api/fetcher';
import { magnetFetcher } from '@/api/fetcher';
import type { IFormBlock } from '@/api/interface';

// ----------------------------------------------------------------------

export const magnetFormBlockQueryKeys = createQueryKeys('magnet_form_block', {
	byId: (id?: string) => {
		const formBlockId = parseIri(id);

		return {
			queryKey: [formBlockId],
			queryFn: () => magnetFetcher<IFormBlock>({ url: `form_blocks/${formBlockId}` })
		};
	}
});
