// utils
import { createQueryKeys } from '@lukemorales/query-key-factory';
import { parseIri } from '@/api/fetcher';
// api
import { magnetFetcher } from '@/api/fetcher';
import type { HydraResponse } from '@/types';
import type { IMagnetMessage } from '@/api/schemas/entities';

// ----------------------------------------------------------------------

export const magnetMessageQueryKeys = createQueryKeys('magnet_message', {
	all: (params?: any) => {
		return {
			queryKey: [params],
			queryFn: () => magnetFetcher<HydraResponse<IMagnetMessage>>({ url: `/messages`, params })
		};
	},
	byId: (id?: string) => {
		const messageId = id ? (parseIri(id) as string) : '';

		return {
			queryKey: [messageId],
			queryFn: () => magnetFetcher<HydraResponse<IMagnetMessage>>({ url: `/messages/${messageId}` })
		};
	}
});
