// utils
import { createQuery<PERSON>eys, type inferQuery<PERSON>eys } from '@lukemorales/query-key-factory';
import { parseIri } from '@/api/fetcher';
// api
import { magnetFetcher } from '@/api/fetcher';
// others
import type { HydraResponse, TGetTreeNodesParams } from '@/types';
import type { IMagnetTreeNode } from '@/api/schemas/entities';

// ----------------------------------------------------------------------

export const magnetTreeNodeQueryKeys = createQueryKeys('magnet_tree_node', {
	all: (params?: TGetTreeNodesParams) => {
		return {
			queryKey: [params || ''],
			queryFn: () => magnetFetcher<HydraResponse<IMagnetTreeNode>>({ url: 'tree_nodes', params })
		};
	},
	byId: (id?: string) => {
		const treeNodeId = parseIri(id);

		return {
			queryKey: [treeNodeId || ''],
			queryFn: () => magnetFetcher<IMagnetTreeNode>({ url: `tree_nodes/${treeNodeId}` }),
			contextQueries: {
				children: {
					queryKey: [''],
					queryFn: () => magnetFetcher<IMagnetTreeNode>({ url: `tree_nodes/${treeNodeId}/children` })
				}
			}
		};
	}
});

export type TQueryKeysMagnetTreeNode = inferQueryKeys<typeof magnetTreeNodeQueryKeys>;
