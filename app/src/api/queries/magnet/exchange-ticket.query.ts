// utils
import { createQueryKeys } from '@lukemorales/query-key-factory';
import { parseIri } from '@/api/fetcher';
// api
import { magnetFetcher } from '@/api/fetcher';
// types
import type { TMagnetTicketsFetcherParams } from '@/types/params';
import type { HydraResponse } from '@/types';
import type { IMagnetTicket } from '@/api/schemas/entities';

// ----------------------------------------------------------------------

export const magnetExchangeTicketQueryKeys = createQueryKeys('exchangeTicket', {
	all: (params?: TMagnetTicketsFetcherParams) => {
		return {
			queryKey: [params || ''],
			queryFn: () => magnetFetcher<HydraResponse<IMagnetTicket>>({ url: `/exchanges/tickets`, params })
		};
	},
	byId: (id?: string | number) => {
		const ticketId = id ? parseIri(id) : '';

		return {
			queryKey: [ticketId],
			queryFn: () => magnetFetcher<IMagnetTicket>({ url: `/exchanges/tickets/${ticketId}` })
		};
	}
});
