// utils
import { createQueryKeys } from '@lukemorales/query-key-factory';
import { parseIri } from '@/api/fetcher';
// api
import type { IMagnetStaffTeam } from '@/api/interface/magnet';
import { magnetFetcher } from '@/api/fetcher';
// types
import type { TMagnetTicketsFetcherParams } from '@/types/params';
import type { HydraResponse } from '@/types';
import type { IMagnetTicket } from '@/api/schemas/entities';

// ----------------------------------------------------------------------

export const magnetStaffTeamQueryKeys = createQueryKeys('magnet_staffTeam', {
	byId: (id?: string | number) => {
		const staffTeamId = id ? parseIri(id) : '';

		return {
			queryKey: [staffTeamId || ''],
			queryFn: () => magnetFetcher<IMagnetStaffTeam>({ url: `staff_team/${staffTeamId}` }),
			contextQueries: {
				tickets: (params?: TMagnetTicketsFetcherParams) => ({
					queryKey: [params],
					queryFn: () =>
						magnetFetcher<HydraResponse<IMagnetTicket>>({
							url: `staff_team/${staffTeamId}/tickets`,
							params
						})
				})
			}
		};
	}
});
