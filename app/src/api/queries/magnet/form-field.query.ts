// utils
import { createQueryKeys } from '@lukemorales/query-key-factory';
import { parseIri } from '@/api/fetcher';
// api
import { magnetFetcher } from '@/api/fetcher';
import type { IMagnetFormField } from '@/api/schemas/entities';

// ----------------------------------------------------------------------

export const magnetFormFieldQueryKeys = createQueryKeys('magnet_form_field', {
	byId: (id?: string) => {
		const formFieldId = parseIri(id);

		return {
			queryKey: [formFieldId],
			queryFn: () => magnetFetcher<IMagnetFormField>({ url: `form_fields/${formFieldId}` })
		};
	}
});
