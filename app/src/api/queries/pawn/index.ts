// utils
import type { inferQueryKeyStore } from '@lukemorales/query-key-factory';
import { mergeQueryKeys } from '@lukemorales/query-key-factory';
// others
import { pawnDomainQueryKeys } from './domain.query';
import { pawnJobQueryKeys } from './job.query';
import { pawnMeQueryKeys } from './me.query';
import { pawnStaffTeamQueryKeys } from './staff-team.query';
import { pawnStaffQueryKeys } from './staff.query';
import { pawnTeamQueryKeys } from './team.query';

// ----------------------------------------------------------------------

export const pawnQueryKeys = mergeQueryKeys(
	pawnDomainQueryKeys,
	pawnJobQueryKeys,
	pawnMeQueryKeys,
	pawnStaffTeamQueryKeys,
	pawnStaffQueryKeys,
	pawnTeamQueryKeys
);

export type PawnQueryKey = inferQueryKeyStore<typeof pawnQueryKeys>;
