// utils
import { createQueryKeys } from '@lukemorales/query-key-factory';
import { parseIri } from '@/api/fetcher';
// api
import type { IPawnStaffTeam } from '@/api/interface/pawn';
import { pawnFetcher } from '@/api/fetcher';
// types
import type { TPaginationParams } from '@/types/params';

// ----------------------------------------------------------------------

export const pawnStaffTeamQueryKeys = createQueryKeys('pawn_staffTeam', {
	byId: (id?: string | number, params?: TPaginationParams) => {
		const staffTeamId = parseIri(id);

		return {
			queryKey: [staffTeamId],
			queryFn: () => pawnFetcher<IPawnStaffTeam>({ url: `/staff_teams/${staffTeamId}`, params })
		};
	}
});
