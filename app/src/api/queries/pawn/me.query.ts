// utils
import { createQueryKeys } from '@lukemorales/query-key-factory';
// api
import type { IAbstractUser } from '@/api/interface';
import { pawnFetcher } from '@/api/fetcher';

// ----------------------------------------------------------------------

interface MeResponse {
	user: IAbstractUser;
	alerts: any[];
}

export const pawnMeQueryKeys = createQueryKeys('pawn_me', {
	me: () => {
		return {
			queryKey: [''],
			queryFn: (): Promise<MeResponse> => pawnFetcher({ url: '/me' })
		};
	}
});
