// utils
import { createQueryKeys } from '@lukemorales/query-key-factory';
import { parseIri } from '@/api/fetcher';
// api
import type { IFiberLogisticOrderItem } from '@/api/interface/fiber';
import { fiberFetcher } from '@/api/fetcher';
import type { TPaginationParams } from '@/types/params';
import type { HydraResponse } from '@/types';

// ----------------------------------------------------------------------

export const fiberLogisticOrdersQueryKeys = createQueryKeys('fiber_logisticOrders', {
	byId: (id?: string) => {
		const logisticOrderId = id ? (parseIri(id) as string) : '';

		return {
			queryKey: [logisticOrderId || ''],
			queryFn: () => fiberFetcher<HydraResponse<IFiberLogisticOrderItem>>({ url: `logistic_orders/${logisticOrderId}` }),
			contextQueries: {
				items: (params: TPaginationParams) => ({
					queryKey: [params || ''],
					queryFn: () =>
						fiberFetcher<HydraResponse<IFiberLogisticOrderItem>>({
							url: `logistic_orders/${logisticOrderId}/items`
						})
				})
			}
		};
	}
});
