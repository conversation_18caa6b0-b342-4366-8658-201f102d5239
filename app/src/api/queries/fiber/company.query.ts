// utils
import { createQueryKeys } from '@lukemorales/query-key-factory';
import { parseIri } from '@/api/fetcher';
// api
import type { ICellCompany } from '@/api/interface/cell';
import type { IFiberBusinessProject } from '@/api/interface/fiber';
import type { IFiberCompany } from '@/api/interface/fiber';
import type { IOrderItem } from '@/api/interface/fiber';
import { fiberFetcher } from '@/api/fetcher';
// types
import type { TFiberGetCompaniesParams, TFiberGetOrdersParams } from '@/types/params';
import type { HydraResponse } from '@/types';

// ----------------------------------------------------------------------

export const fiberCompanyQueryKeys = createQueryKeys('fiber_company', {
	all: (params: TFiberGetCompaniesParams) => ({
		queryKey: [''],
		queryFn: () => fiberFetcher<HydraResponse<ICellCompany>>({ url: 'companies', params })
	}),
	byName: (name: string) => ({
		queryKey: [name],
		queryFn: () => fiberFetcher<HydraResponse<ICellCompany>>({ url: 'companies', params: { name } })
	}),
	byId: (id: string | undefined) => {
		const companyId = id ? (parseIri(id) as string) : '';

		return {
			queryKey: [companyId],
			queryFn: () => fiberFetcher<IFiberCompany>({ url: `companies/${companyId}` }),
			contextQueries: {
				orders: (params?: TFiberGetOrdersParams) => ({
					queryKey: [companyId, params],
					queryFn: () =>
						fiberFetcher<HydraResponse<IOrderItem>>({
							url: `companies/${companyId}/orders` || '',
							params
						})
				}),
				businessProjects: () => ({
					queryKey: [companyId || ''],
					queryFn: () =>
						fiberFetcher<HydraResponse<IFiberBusinessProject>>({
							url: `companies/${companyId}/business_projects` || ''
						})
				})
			}
		};
	}
});
