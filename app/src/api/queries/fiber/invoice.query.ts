// utils
import { createQueryKeys } from '@lukemorales/query-key-factory';
import { parseIri } from '@/api/fetcher';
// api
import type { IFiberVLAN } from '@/api/interface/fiber';
import { fiberFetcher } from '@/api/fetcher';

// ----------------------------------------------------------------------

export const fiberInvoiceQueryKeys = createQueryKeys('fiber_invoice', {
	// all: () => ({
	// 	queryKey: [''],
	// 	queryFn: () => fiberFetcher<HydraResponse<ICellCompany>>({ url: 'vlans', params })
	// }),
	byId: (id?: string) => {
		const invoiceId = id ? (parseIri(id) as string) : '';

		return {
			queryKey: [invoiceId],
			queryFn: () => fiberFetcher<IFiberVLAN>({ url: `invoices/${invoiceId}` }),
			contextQueries: {
				pdf: {
					queryKey: [invoiceId],
					queryFn: () =>
						fiberFetcher({
							url: `invoices/${invoiceId}/pdf`,
							config: {
								responseType: 'blob',
								headers: {
									'Content-Type': 'application/pdf',
									Accept: 'application/pdf'
								}
							}
						})
				}
			}
		};
	}
});
