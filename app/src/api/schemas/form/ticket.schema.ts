// utils
import { z } from 'zod';

// ----------------------------------------------------------------------

export const ticketCreateFormSchema = z.object({
	reference: z.string().min(1, 'valeur requise'),
	space: z.string().min(1, 'valeur requise'),
	form: z.string(),
	team: z.string(),
	priority: z.string().min(1, 'valeur requise'),
	details: z.record(z.string(), z.union([z.string(), z.boolean()]))
});

export type TTicketCreateFormSchema = z.infer<typeof ticketCreateFormSchema>;
