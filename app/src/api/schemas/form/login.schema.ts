// utils
import { object, string, type z } from 'zod';
// locales
import { REQUIRED_FIELD } from '@/lib/i18n/constante.i18n';

// ----------------------------------------------------------------------

export const LoginSchema = object({
	uid: string({
		message: REQUIRED_FIELD
	}).min(1, {
		message: REQUIRED_FIELD
	}),
	password: string({
		message: REQUIRED_FIELD
	}).min(1, {
		message: REQUIRED_FIELD
	})
	// _remember_me: boolean()
});

export type ILoginSchema = z.infer<typeof LoginSchema>;
