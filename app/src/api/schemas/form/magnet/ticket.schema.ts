// utils
import { z } from 'zod';

// ----------------------------------------------------------------------
export const details = z.object({
	formField: z.string(),
	value: z.boolean().or(z.string()).or(z.number())
});

export const ticketCreateFormSchema = z.object({
	form: z.string().optional(),
	team: z
		.string()
		.optional()
		.optional()
		.refine((data) => data && data !== '', { message: 'Champs requis' }),
	priority: z
		.string()
		.optional()
		.refine((data) => data && data !== '', { message: 'Champs requis' }),
	reference: z.string().optional(),
	details: z
		.array(
			z.object({
				formField: z.string(),
				value: z.union([z.boolean(), z.string(), z.number()]),
				tmp: z.object({
					formFieldConditions: z
						.array(
							z.object({
								'@id': z.string(),
								type: z.string()
							})
						)
						.optional(),
					required: z.boolean()
				})
			})
		)
		.superRefine((val, ctx) => {
			val.forEach((item, i) => {
				if (item.tmp.required && item.value === '')
					ctx.addIssue({
						code: z.ZodIssueCode.custom,
						message: 'Champs requis',
						fatal: true,
						path: [`${i}.value`]
					});
			});
			return z.NEVER;
		})
		.transform((data) => {
			const newDetails = [];

			for (let index = 0; index < data.length; index++) {
				const { tmp, ...rest } = data[index];
				const formFieldConditions = tmp.formFieldConditions || [];

				if (formFieldConditions.find((item) => item.type === 'hidden')) continue;
				newDetails.push(rest);
			}

			return newDetails;
		})
});

export type TTicketCreateFormSchema = z.infer<typeof ticketCreateFormSchema>;
