// utils
import { any, object, z } from 'zod';
// api
import { dateSchema } from '@/api/schemas/common';

// ----------------------------------------------------------------------

enum EMagnetPriority {
	low,
	normal,
	high,
	urgent
}

/** State workflow */
enum EMagnetState {
	created,
	processed
}

export const magnetTicketEntitySchema = object({
	reference: any(),
	/** @param space_id Table space */
	space: any(),
	/** @param form_id Table form */
	form: any(),
	/** @param created_by_team_id (PAWN) */
	createdByTeam: any(),
	/** @param created_by (PAWN) */
	createdBy: any(),
	comment: any(),
	/** @param close_comment */
	closeComment: any(),
	state: z.nativeEnum(EMagnetState),
	priority: z.nativeEnum(EMagnetPriority),
	discr: any()
})
	.merge(dateSchema)
	.required()
	.partial({
		createdByTeam: true,
		closeComment: true,
		deletedAt: true,
		comment: true
	});
