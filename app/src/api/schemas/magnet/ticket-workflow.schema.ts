// utils
import { z } from 'zod';
// api
import { dateSchema } from '@/api/schemas/common';

// ----------------------------------------------------------------------

export const magnetTicketWorkflowEntitySchema = z
	.object({
		/** @param ticket_id (Table ticket) */
		ticket: z.string(),
		/** @param from_state */
		fromState: z.string(),
		/** @param to_state */
		toState: z.string(),
		/** @param transition_name */
		transitionName: z.string(),
		/** @param triggered_by (PAWN /staff/id) */
		triggeredBy: z.string(),
		duration: z.string(),
		comment: z.string()
	})
	.merge(
		dateSchema.pick({
			createdAt: true
		})
	)
	.required()
	.partial({
		comment: true
	});

export type TMagnetTicketWorkflowEntitySchema = z.infer<typeof magnetTicketWorkflowEntitySchema>;
export type TMagnetTicketWorkflowErrorEntitySchema = z.infer<typeof magnetTicketWorkflowEntitySchema>;
