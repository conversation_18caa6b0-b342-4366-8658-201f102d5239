// utils
import { any, date, record, string, type z } from 'zod';
// api
import { dateSchema, entityJsonLdSchema } from '../commun';
// others
import { pawnNotificationTemplateEntitySchema } from './notificationTemplate.schemas';

// ----------------------------------------------------------------------

export const pawnNotificationEntitySchema = entityJsonLdSchema
	.extend({
		content: string(),
		params: record(string(), any()),
		template: pawnNotificationTemplateEntitySchema.pick({
			'@context': true,
			'@id': true,
			'@type': true,
			name: true,
			shortCode: true,
			title: true
		}),
		type: string(),
		level: string(),
		readAt: date(),
		emailSentAt: date()
	})
	.merge(
		dateSchema.pick({
			createdAt: true
		})
	)
	.partial()
	.required({
		'@id': true,
		createdAt: true,
		content: true,
		params: true,
		template: true
	});

export interface IPawnNotification extends z.infer<typeof pawnNotificationEntitySchema> {}
