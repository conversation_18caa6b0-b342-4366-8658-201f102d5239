// utils
import { array, number, object, string, z } from 'zod';
// api
import { entityJsonLdSchema, paginationParamsSchema } from '../commun';

// ----------------------------------------------------------------------

const JOBTYPES = ['commercial', 'marketing', 'rh', 'support', 'tech', 'other'] as const;

const JobTypeEnum = z.enum(JOBTYPES);

export const pawnJobEntitySchema = object({
	name: string(),
	irmId: number(),
	type: JobTypeEnum,
	memberCount: number()
})
	.merge(entityJsonLdSchema)
	.partial()
	.required({
		'@id': true
	});

export const pawnJobParamsSchema = paginationParamsSchema
	.extend({
		name: string(),
		type: string(),
		'name[]': array(string())
	})
	.partial();

export type TPawnJobsParams = z.infer<typeof pawnJobParamsSchema>;
export interface IPawnJob extends z.infer<typeof pawnJobEntitySchema> {}
