// utils
import { boolean, string, type z } from 'zod';
// api
import { entityJsonLdSchema } from '../commun';

// ----------------------------------------------------------------------

export const pawnNotificationTemplateEntitySchema = entityJsonLdSchema
	.extend({
		name: string(),
		shortCode: string(),
		title: string(),
		category: string(),
		canUseEmail: boolean(),
		canUseSms: boolean(),
		canUseWeb: boolean(),
		canUseApp: boolean()
	})
	.partial()
	.required({
		'@id': true,
		'@type': true,
		name: true,
		shortCode: true,
		title: true
	});

export interface IPawnNotificationTemplate extends z.infer<typeof pawnNotificationTemplateEntitySchema> {}
