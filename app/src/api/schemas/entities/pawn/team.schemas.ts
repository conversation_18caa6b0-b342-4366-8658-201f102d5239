// utils
import { array, number, string, type z } from 'zod';
// api
import { entityJsonLdSchema, staffRoleEnumSchema } from '../commun';

// ----------------------------------------------------------------------

export const pawnTeamEntitySchema = entityJsonLdSchema
	.extend({
		name: string(),
		memberCount: number(),
		roles: array(staffRoleEnumSchema),
		domain: entityJsonLdSchema.extend({
			name: string()
		}),
		team: string(),
		subTeams: array(string()),
		avatar: string()
	})
	.partial()
	.required({
		'@id': true,
		roles: true
	});

export interface IPawnTeam extends z.infer<typeof pawnTeamEntitySchema> {}
