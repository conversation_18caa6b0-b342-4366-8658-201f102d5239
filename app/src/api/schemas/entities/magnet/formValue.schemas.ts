// utils
import { array, string, z } from 'zod';
// api
import { entityJsonLdSchema } from '../commun';
import { magnetFormFieldEntitySchema } from '@/api';

// ----------------------------------------------------------------------

export const magnetFormValueEntitySchema = entityJsonLdSchema
	.extend({
		value: z.string(),
		ticket: z.string(),
		formField: array(
			magnetFormFieldEntitySchema.pick({
				'@context': true,
				'@id': true,
				'@type': true,
				name: true,
				type: true,
				inputType: true,
				label: true,
				validation: true,
				enum: true,
				required: true
			})
		),
		documents: array(string())
	})
	.partial()
	.required({
		'@id': true
	});

export type TMagnetFormValueEntitySchema = z.infer<typeof magnetFormValueEntitySchema>;
