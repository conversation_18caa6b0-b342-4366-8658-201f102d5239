// utils
import { array, boolean, string, z } from 'zod';
// api
import { entityJsonLdSchema } from '../commun';
import { EFormFieldTypeFrontId, EFormFieldTypeId } from '@/api';

// ----------------------------------------------------------------------

const formFieldSchema = entityJsonLdSchema
	.extend({
		name: string(),
		type: z.enum(EFormFieldTypeId),
		inputType: z.enum(EFormFieldTypeFrontId),
		label: string(),
		validation: string(),
		enum: array(string()),
		required: boolean()
	})
	.required({
		'@id': true
	});

export const magnetFormFieldConditionEntitySchema = entityJsonLdSchema
	.extend({
		reference: string(),
		type: string(),
		value: string(),
		formFieldParent: formFieldSchema,
		formFieldChilds: array(formFieldSchema)
	})
	.required({
		'@id': true
	});

export interface IMagnetFormFieldCondition extends z.infer<typeof magnetFormFieldConditionEntitySchema> {}
