// utils
import { boolean, object, z } from 'zod';
// api
import { entityJsonLdSchema } from '../commun';

// ----------------------------------------------------------------------

export const magnetDocumentEntitySchema = entityJsonLdSchema.extend({
	description: z.string(),
	filePath: z.string(),
	extension: z.string(),
	uuid: z.string()
});
export const magnetDocumentParamsSchema = object({
	showDocument: boolean().optional()
});

export type TMagnetDocumentParams = z.infer<typeof magnetDocumentParamsSchema>;
export interface IMagnetDocument extends z.infer<typeof magnetDocumentEntitySchema> {}
