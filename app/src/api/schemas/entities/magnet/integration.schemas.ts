// utils
import { z } from 'zod';
// api
import { dateSchema } from '../commun';

// ----------------------------------------------------------------------

export const magnetIntegrationEntitySchema = z
	.object({
		/** @param external_id (PAWN table user) */
		external: z.string(),
		/** @param ticket_id (table ticket) */
		ticket: z.string(),
		/** @param outsource_id (outsource ???) */
		outsource: z.string()
	})
	.merge(
		dateSchema.omit({
			expectedAt: true
		})
	)
	.required()
	.partial({
		deletedAt: true,
		external: true
	});

export type TMagnetIntegrationEntitySchema = z.infer<typeof magnetIntegrationEntitySchema>;
