// utils
import { array, object, string, z } from 'zod';
// api
import { dateSchema, entityJsonLdSchema, paginationParamsSchema, sortEnumSchema } from '../commun';

// ----------------------------------------------------------------------

export const magnetMessageEntitySchema = entityJsonLdSchema
	.extend({
		content: z.string(),
		user: entityJsonLdSchema.extend({
			iri: z.string(),
			team: z.string(),
			domain: z.string()
		}),
		team: string(),
		documents: array(string()),
		outsources: array(string())
	})
	.merge(
		dateSchema.pick({
			createdAt: true
		})
	)
	.required({
		'@id': true
	});

export const magnetMessagesParamsSchema = paginationParamsSchema
	.extend({
		'order[createdAt]': sortEnumSchema
	})
	.partial();

export const magnetMessagesDataSchema = object({
	content: string(),
	ticket: string(),
	team: string(),
	documents: array(string()),
	outsources: array(string())
})
	.partial()
	.required({
		content: true,
		ticket: true,
		team: true
	});

export type TMagnetMessagesData = z.infer<typeof magnetMessagesDataSchema>;
export type TMagnetMessagesParams = z.infer<typeof magnetMessagesParamsSchema>;
export interface IMagnetMessage extends z.infer<typeof magnetMessageEntitySchema> {}
