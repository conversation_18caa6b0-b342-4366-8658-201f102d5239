// utils
import { array, boolean, object, string, union, type z } from 'zod';
// api
import { dateSchema, entityJsonLdSchema } from '../commun';
import { magnetFormEntitySchema, paginationParamsSchema } from '@/api';

// ----------------------------------------------------------------------

export const magnetTreeNodeEntitySchema = entityJsonLdSchema
	.extend({
		name: string(),
		domain: string(),
		space: string(),
		isRoot: boolean(),
		forms: array(
			union([
				string(),
				magnetFormEntitySchema.pick({
					'@context': true,
					'@id': true,
					'@type': true,
					name: true
				})
			])
		),
		team: string()
	})
	.merge(
		dateSchema.omit({
			expectedAt: true
		})
	)
	.required();

export const magnetTreeNodeChildrenEntitySchema = object({
	treeNode: object({
		children: array(
			object({
				name: string()
			}).merge(
				entityJsonLdSchema.pick({
					'@id': true
				})
			)
		)
	}).merge(
		entityJsonLdSchema.pick({
			'@id': true
		})
	)
});

export const magnetTreeNodesParamsSchema = paginationParamsSchema.extend({
	isRoot: boolean(),
	space: string(),
	'space[]': array(string()),
	domain: string(),
	'domain[]': array(string())
});

export type TMagnetTreeNodesParams = z.infer<typeof magnetTreeNodesParamsSchema>;
export interface IMagnetTreeNode extends z.infer<typeof magnetTreeNodeEntitySchema> {}
export interface IMagnetTreeNodeChildren extends z.infer<typeof magnetTreeNodeChildrenEntitySchema> {}
