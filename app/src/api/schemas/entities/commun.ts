// utils
import { date, number, object, string, z } from 'zod';

// ----------------------------------------------------------------------

export const dateSchema = z.object({
	createdAt: date(),
	updatedAt: date(),
	expectedAt: date().nullable(),
	deletedAt: date().nullable(),
	openedAt: date().nullable()
});

export const entityJsonLdSchema = object({
	'@context': string(),
	'@id': string(),
	'@type': string()
}).required();

export const strictCreatedAtSchema = z.object({
	'createdAt[before]': string(),
	'createdAt[strictly_before]': string(),
	'createdAt[after]': string(),
	'createdAt[strictly_after]': string()
});

export const sortEnumSchema = z.enum(['asc', 'desc']);

export const paginationParamsSchema = z.object({
	page: number(),
	itemsPerPage: number()
});

export const staffRolesEnum = [
	'ROLE_ADMIN',
	'ROLE_USER',
	'ROLE_STAFF',
	'ROLE_STAFF_MANAGER',
	'ROLE_MANAGER',
	'ROLE_RETAILER',
	'ROLE_MANAGE_TICKET',
	'ROLE_CREATE_TICKET',
	'ROLE_REPLY_TICKET',
	'ROLE_READ_TICKET',
	// old deprecates
	'MAGNET_READ_TICKET',
	'MAGNET_MANAGER',
	'MAGNET_MANAGE_TICKET',
	'MAGNET_REPLY_TICKET',
	'MAGNET_CREATE_TICKET'
] as const;
export const staffRoleEnumSchema = z.enum(staffRolesEnum);

export type TStaffRoles = z.infer<typeof staffRoleEnumSchema>;
export type TPaginationParams = z.infer<typeof paginationParamsSchema>;
