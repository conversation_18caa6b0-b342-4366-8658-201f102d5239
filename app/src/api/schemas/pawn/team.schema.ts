// utils
import type { z } from 'zod';
import { array, object, string } from 'zod';
// locales
import { REQUIRED_FIELD } from '@/lib/i18n/constante.i18n';

// ----------------------------------------------------------------------

export const TeamSchema = object({
	name: string().refine((data) => !!data, {
		message: REQUIRED_FIELD
	}),
	domain: string().refine((data) => !!data, {
		message: REQUIRED_FIELD
	}),
	roles: array(
		string().refine((data) => !!data, {
			message: REQUIRED_FIELD
		})
	),
	subTeams: array(
		object({
			name: string().refine((data) => !!data, {
				message: REQUIRED_FIELD
			}),
			roles: array(
				string().refine((data) => !!data, {
					message: REQUIRED_FIELD
				})
			)
		})
	)
});

export type ITeamSchema = z.infer<typeof TeamSchema>;
