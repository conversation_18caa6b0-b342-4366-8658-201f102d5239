// tanstack
import { useMutation } from '@tanstack/react-query';
// utils
import { API_ENDPOINTS_MAGNET } from '@/utils/axios';
// api
import type { ITicketBase } from '@/api/interface';
import type { TTicketCreateFormSchema } from '@/api/schemas/form/magnet';
import { magnetFetcher } from '@/api/fetcher';
import { magnetQueryKeys } from '@/api/queries';
// services
import { queryClient } from '@/services/query-client.service';
// types
import type { IApiPlatformError } from '@/types';

// ----------------------------------------------------------------------

export function useCreateTicket() {
	const URL = API_ENDPOINTS_MAGNET.ticket.root;

	return useMutation<ITicketBase, IApiPlatformError, TTicketCreateFormSchema>({
		mutationFn: (data) => magnetFetcher({ url: URL, method: 'post', data }),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: magnetQueryKeys.magnet_space.all._def });
			queryClient.invalidateQueries({ queryKey: magnetQueryKeys.magnet_ticket.all._def });
		}
	});
}
