// tanstack
import { useMutation } from '@tanstack/react-query';
// utils
import { API_ENDPOINTS_PAWN } from '@/utils/axios';
// api
import type { IJobForm, IJobItem } from '@/api/interface/pawn';
import type { IApiPlatformError } from '@/api/types';
import { pawnFetcher } from '@/api/fetcher';
import { pawnQueryKeys } from '@/api/queries';
// services
import { queryClient } from '@/services/query-client.service';

// ----------------------------------------------------------------------

export function useCreateJob() {
	const URL = API_ENDPOINTS_PAWN.jobs;

	return useMutation<IJobItem, IApiPlatformError, IJobForm>({
		mutationFn: (data) => pawnFetcher({ url: URL, method: 'post', data }),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: pawnQueryKeys.pawn_job.all._def });
		}
	});
}

export function useUpdateJob({ jobIri }: { jobIri: string }) {
	return useMutation<IJobItem, IApiPlatformError, IJobForm>({
		mutationFn: (data) =>
			pawnFetcher({
				url: jobIri,
				method: 'patch',
				data,
				config: {
					headers: {
						'Content-Type': 'application/merge-patch+json'
					}
				}
			}),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: pawnQueryKeys.pawn_job.all._def });
		}
	});
}

export function useDeleteJob() {
	return useMutation<IJobItem, Error, string>({
		mutationFn: (jobIri) => pawnFetcher({ url: jobIri, method: 'delete' }),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: pawnQueryKeys.pawn_job.all._def });
		}
	});
}
