// tanstack
import { useMutation } from '@tanstack/react-query';
// utils
import { API_ENDPOINTS_PAWN } from '@/utils/axios';
// api
import type { IStaffItem } from '@/api/interface/pawn';
import type { IApiError, IApiPlatformError } from '@/api/types';
import { pawnFetcher } from '@/api/fetcher';
import { pawnQueryKeys } from '@/api/queries';
// services
import { queryClient } from '@/services/query-client.service';
// others
import type { IStaffForm } from '@/api/schemas/form/staff.schema';

// ----------------------------------------------------------------------

export function useCreateStaff() {
	const URL = API_ENDPOINTS_PAWN.staff.root;

	return useMutation<IStaffItem, IApiPlatformError, IStaffForm>({
		mutationFn: (data) => pawnFetcher({ url: URL, method: 'post', data }),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: pawnQueryKeys.pawn_staff.all._def });
		}
	});
}

export function useUpdateStaff({ staffIri }: { staffIri: string }) {
	return useMutation<IStaffItem, IApiPlatformError, IStaffForm>({
		mutationFn: (data) =>
			pawnFetcher({
				url: staffIri,
				method: 'patch',
				data,
				config: {
					headers: {
						'Content-Type': 'application/merge-patch+json'
					}
				}
			}),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: pawnQueryKeys.pawn_staff.all._def });
		}
	});
}

export function useSetStaffPassword({ staffIri, token }: { staffIri: string; token: string }) {
	return useMutation<IStaffItem, IApiError, string>({
		mutationFn: (password) =>
			pawnFetcher({
				url: staffIri,
				method: 'patch',
				data: { password },
				config: {
					withCredentials: false,
					headers: {
						Authorization: `AuthToken ${token}`,
						'Content-Type': 'application/merge-patch+json'
					}
				}
			}),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: pawnQueryKeys.pawn_staff.all._def });
		}
	});
}

export function useResetStaffPassword() {
	const URL = API_ENDPOINTS_PAWN.staff.resetPassword;

	return useMutation<IStaffItem, IApiError, string>({
		mutationFn: (email) =>
			pawnFetcher({
				url: URL,
				method: 'post',
				config: {
					withCredentials: false
				},
				data: { email }
			}),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: pawnQueryKeys.pawn_staff.all._def });
		}
	});
}

export function useDeleteStaff() {
	return useMutation<IStaffItem, IApiPlatformError, string>({
		mutationFn: (uri) => pawnFetcher({ url: uri, method: 'delete' }),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: pawnQueryKeys.pawn_staff.all._def });
		}
	});
}
