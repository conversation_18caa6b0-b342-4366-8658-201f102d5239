import { API_ENDPOINTS_FIBER } from '@/utils/axios.ts';
import { useMutation } from '@tanstack/react-query';
import type { IApiPlatformError } from '@/api/types';
import { fiberFetcher } from '@/api/fetcher';
import { queryClient } from '@/services/query-client.service.ts';
import { fiberQueryKeys } from '@/api/queries';
import type { TFiberPutOrderParams, TFiberPostOrderParams } from '@/types/params';
import type { IOrderItem } from '@/api/interface';

export function useEditOrder({ orderId }: { orderId: string }) {
	const URL = API_ENDPOINTS_FIBER.order.root;

	return useMutation<IApiPlatformError, IApiPlatformError, TFiberPutOrderParams>({
		mutationFn: (data) => fiberFetcher({ url: `${URL}/${orderId}`, method: 'put', data }),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: fiberQueryKeys.fiber_order.all._def });
			queryClient.invalidateQueries({ queryKey: fiberQueryKeys.fiber_order.byId(orderId).queryKey });
		}
	});
}

export function useCreateOrder() {
	const URL = API_ENDPOINTS_FIBER.order.root;

	return useMutation<IOrderItem, IApiPlatformError, TFiberPostOrderParams>({
		mutationFn: (data) => fiberFetcher({ url: URL, method: 'post', data }),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: fiberQueryKeys.fiber_order.all._def });
		}
	});
}
