import { Fragment } from 'react';
// tanstack
import { keepPreviousData, useQuery } from '@tanstack/react-query';
// utils
import { translateFiberIncidentOrigin, translateFiberIncidentType } from '@/utils/common.utils';
import { Loading } from '@tools/reactore';
import { Typography } from '@tools/reactore';
import { format, isBefore } from 'date-fns';
import { twJoin } from 'tailwind-merge';
// api
import { queries } from '@/api/queries';
// components
import TableRowAccordion from '@/components-old/ui/table-row-accordion';
import TableRowAccordionDetails from '@/components-old/ui/table-row-accordion/table-row-accordion-details';
import TableRowAccordionSummary from '@/components-old/ui/table-row-accordion/table-row-accordion-summary';
import { CustomPageHeader } from '@/components/custom';

// ----------------------------------------------------------------------

export default function FiberParkManagementView() {
	const { data: incidents, isLoading: isLoadingIncidents } = useQuery({
		...queries.fiber_incidents.all({ page: 1, itemsPerPage: 30 }),
		placeholderData: keepPreviousData
	});

	return (
		<>
			<CustomPageHeader
				title='Gestion du parc'
				description='Retrouvez ici la liste des incidents en cours et passés sur le réseau.'
			/>

			{Boolean(isLoadingIncidents) && <Loading />}
			<div className={twJoin('w-full py-12', isLoadingIncidents && 'hidden')}>
				<div className='card w-full px-6 py-8 font-bold'>
					<Typography className='text-3xl'>{incidents?.['hydra:totalItems']} incidents</Typography>
					<table className='w-full table-auto border-separate border-spacing-x-2 border-spacing-y-4 text-left'>
						<thead>
							<tr className='child:caption child:font-light child:text-free-gray-500'>
								<th>État</th>
								<th>Date</th>
								<th>Type</th>
								<th>Impact</th>
								<th>Date de fin prévue</th>
								{/* <th>Commandes impactées</th> */}
								<th className='desktop:w-105'>Description</th>
							</tr>
						</thead>
						<tbody className='text-ellipsis text-sm font-normal child:whitespace-nowrap'>
							{incidents?.['hydra:member'].map((incident) => (
								<Fragment key={incident['@id']}>
									<TableRowAccordion>
										<TableRowAccordionSummary>
											<td>
												<span
													className={twJoin(
														incident.level === 'Service degrade' ? 'badge-error' : 'badge-warning'
													)}>
													{incident.level}
												</span>
											</td>
											<td>{incident.startedAt ? format(new Date(incident.startedAt), 'dd/MM/yyyy HH:mm') : 'N/A'}</td>
											<td>{translateFiberIncidentType(incident.type)}</td>
											<td>{incident.origin ? translateFiberIncidentOrigin(incident.origin) : 'N/A'}</td>
											<td>
												{incident.endedAt ? (
													<span
														className={twJoin(
															isBefore(new Date(), new Date(incident.endedAt))
																? 'font-semibold text-free-red'
																: '',
															'font-semibold text-free-red'
														)}>
														{incident.endedAt ? format(new Date(incident.endedAt), 'dd/MM/yyyy HH:mm') : 'N/A'}
													</span>
												) : (
													'N/A'
												)}
											</td>
											{/* <td></td> */}
											<td className='max-w-105'>
												<div className='w-full overflow-hidden text-ellipsis'>{incident.comment || 'N/A'}</div>
											</td>
										</TableRowAccordionSummary>
										<TableRowAccordionDetails>
											<Typography className='caption mb-2 text-free-gray-500'>Description de l'incident</Typography>
											<Typography className='w-full whitespace-normal'>{incident.comment}</Typography>
										</TableRowAccordionDetails>
									</TableRowAccordion>
								</Fragment>
							))}
						</tbody>
					</table>
				</div>
			</div>
		</>
	);
}
