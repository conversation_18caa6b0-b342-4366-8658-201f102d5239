// utils
import { twJoin } from 'tailwind-merge';

// ----------------------------------------------------------------------

type Props = { label: string; tab: string[]; handleClick: (value: string) => void; value: string };

export default function DropdownAppointment({ label, tab, handleClick, value }: Props) {
	return (
		<div
			className='flex h-full w-full flex-col items-start justify-between rounded-lg bg-white p-2 dark:bg-free-dark-smoke'
			onClick={(e) => e.stopPropagation()}
			aria-hidden='true'>
			<div className='px-2 pb-2 font-montserrat text-[10px] text-slate-500 dark:text-gray-200'>{label}</div>
			{tab.map((dt, index) => {
				return (
					<button
						key={`key${index}`}
						className={twJoin(
							'w-full cursor-pointer rounded-md text-left hover:bg-slate-50 dark:hover:bg-free-dark-smoke-light',
							value === dt ? 'bg-slate-100' : ''
						)}
						onClick={() => {
							handleClick(dt);
						}}>
						<div className='px-2 py-1 font-montserrat text-sm text-slate-700 dark:text-gray-200'>{dt}</div>
					</button>
				);
			})}
		</div>
	);
}
