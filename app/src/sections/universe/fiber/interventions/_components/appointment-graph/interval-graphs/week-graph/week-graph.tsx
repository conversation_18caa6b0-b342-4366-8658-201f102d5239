// sections
import GraphDayContainer from '../../graph-day-container/graph-day-container';
import type { TOptionFilter } from '../interval-graphs';

// ----------------------------------------------------------------------

type Props = {
	intervalMetrics: TOptionFilter[];
};

export default function WeekGraph({ intervalMetrics }: Props) {
	return (
		<div className='flex h-full w-full select-none items-end justify-center'>
			{intervalMetrics.map((dayMetrics, ind) => {
				return (
					<GraphDayContainer key={`key${ind}`} dayMetrics={dayMetrics} isLast={ind === intervalMetrics.length - 1} index={ind} />
				);
			})}
		</div>
	);
}
