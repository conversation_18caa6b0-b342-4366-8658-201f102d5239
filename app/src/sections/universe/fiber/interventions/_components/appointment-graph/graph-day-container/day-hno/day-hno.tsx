// utils
import { twJoin } from 'tailwind-merge';

// ----------------------------------------------------------------------

type TDayHnoProps = {
	className: string;
	datePast: boolean;
	index: number;
	isHno?: boolean;
};

export default function DayHno({ className, datePast, index, isHno }: TDayHnoProps) {
	const tab = [0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1];

	return (
		<div className={twJoin('h-full w-full overflow-hidden', className, !(index % 2) ? '' : 'pt-[15px]')}>
			{tab.map((_, key) => {
				return (
					<div key={key} className={twJoin('w-[110% ml-[-5px] h-[30px]', isHno ? 'ml-[-30px] w-[130px]' : 'ml-[-7px]')}>
						<div
							className={twJoin(
								'h-[50%] w-[130%] -rotate-[25.5deg] bg-[#FDBA74]/20',
								datePast ? 'bg-[#A855F7]/20' : 'bg-[#F97316]/70'
							)}
						/>
						<div className='bg-[#FDBA74]/7 h-[50%] w-[130%] -rotate-[25.5deg]' />
					</div>
				);
			})}
		</div>
	);
}
