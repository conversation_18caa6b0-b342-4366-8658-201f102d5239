// sections
import AppointmentLegends from './appointment-legends/appointment-legends';
import BarMonths from './bar-months';
import FilterAppointment from './filter-appointment';
import IntervalGraphs from './interval-graphs/interval-graphs';

// ----------------------------------------------------------------------

export default function AppointmentGraph() {
	return (
		<div className='flex w-full flex-col'>
			<BarMonths />
			<FilterAppointment />
			<div className='mb-4 mt-[-8px] flex w-[1280px] select-none space-x-2 '>
				<div className='flex h-[380px] w-[100%] items-end justify-end rounded-2xl'>
					<IntervalGraphs />
				</div>
			</div>
			<AppointmentLegends />
		</div>
	);
}
