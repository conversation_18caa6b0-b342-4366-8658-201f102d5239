// sections
import GraphDayContainer from '../../graph-day-container/graph-day-container';
import type { TOptionFilter } from '../interval-graphs';

// ----------------------------------------------------------------------

type Props = {
	tabInfo: TOptionFilter[];
};

export default function WeekendGraph({ tabInfo }: Props) {
	const weekendMetrics = tabInfo.reduce(
		(acc, curr, _, array) => {
			acc.day = array[0]?.day || new Date();
			for (const metric in curr.outsideBusinessHours) {
				if (!acc.outsideBusinessHours[metric]) {
					acc.outsideBusinessHours[metric] = {};
				}
				for (const total in curr.outsideBusinessHours[metric]) {
					if (!acc.outsideBusinessHours[metric][total]) {
						acc.outsideBusinessHours[metric][total] = 0;
					}
					acc.outsideBusinessHours[metric][total] += curr.outsideBusinessHours[metric][total];
					acc.total += curr.outsideBusinessHours[metric][total];
				}
			}
			for (const metric in curr.withinBusinessHours) {
				if (!acc.withinBusinessHours[metric]) {
					acc.withinBusinessHours[metric] = {};
				}
				for (const total in curr.withinBusinessHours[metric]) {
					if (!acc.withinBusinessHours[metric][total]) {
						acc.withinBusinessHours[metric][total] = 0;
					}
					acc.withinBusinessHours[metric][total] += curr.withinBusinessHours[metric][total];
					acc.total += curr.withinBusinessHours[metric][total];
				}
			}

			return acc;
		},
		{
			day: new Date(),
			outsideBusinessHours: {},
			withinBusinessHours: {},
			total: 0
		}
	) as unknown as TOptionFilter;

	return (
		<div className='flex h-full min-w-[32px] select-none items-end rounded-t-2xl'>
			<GraphDayContainer dayMetrics={weekendMetrics} isLast index={0} />
		</div>
	);
}
