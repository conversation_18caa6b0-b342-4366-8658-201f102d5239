import { useEffect, useState } from 'react';
// utils
import clsx from 'clsx';
import { addDays, format, isBefore, isSameDay, set } from 'date-fns';
import { fr } from 'date-fns/locale';
// hooks
import { useMetricsAppointment } from '@/hooks/queries/use-metrics-appointments';
// sections
import type { TOptionFilter } from '../../interval-graphs/interval-graphs';
import BarDay from './bar-day';
import { useAppointmentsSelectedLegends } from '../../../../_hooks/atoms/use-appointments-selected-legends';
import { useAppointmentsSelectedMetrics } from '../../../../_hooks/atoms/use-appointments-selected-metrics';
import { useAppointmentsSelectedDay } from '@/sections/universe/fiber/interventions/_hooks/atoms/use-appointments-selected-day';
// others
import { metricColors } from './bar-day/colors.utils';

// ----------------------------------------------------------------------

type Props = {
	dayMetrics: TOptionFilter;
	isToolTip: boolean;
};

export type TTabStand = Array<{ metric: string; total: number; color: string }>;

export default function DaySelector({ dayMetrics, isToolTip }: Props) {
	const [selectedLegend] = useAppointmentsSelectedLegends();
	const { maxDailyAppointments } = useMetricsAppointment();
	const [selectedMetrics] = useAppointmentsSelectedMetrics();
	const [selectedDay, setSelectedDay] = useAppointmentsSelectedDay();

	const [height, setHeight] = useState(0);
	const [filteredAppointmentsByState, setFilteredAppointmentsByState] = useState<TTabStand>();
	const [todayPlannedAppointments, setTodayPlannedAppointments] = useState<TTabStand>();

	const datePast = isBefore(dayMetrics.day, addDays(set(new Date(), { hours: 2, minutes: 0, seconds: 0, milliseconds: 0 }), 1));

	const isToday = isSameDay(dayMetrics.day, new Date());

	useEffect(() => {
		let tabStand: TTabStand = [];

		const getPlannedTabStand = (dayMetrics: TOptionFilter) => {
			return dayMetrics.withinBusinessHours?.[selectedMetrics.planned] && dayMetrics.outsideBusinessHours?.[selectedMetrics.planned]
				? [
						...Object.entries(dayMetrics.withinBusinessHours[selectedMetrics.planned])
							.filter((item) => {
								if (selectedLegend?.planned?.withinBusinessHours?.length)
									return selectedLegend.planned.withinBusinessHours.includes(item[0]);

								if (selectedLegend.planned.outsideBusinessHours?.length) return null;

								return item;
							})
							.map(([metric, total]) => {
								return {
									metric,
									total,
									color: metricColors.planned[selectedMetrics.planned][metric]?.withinBusinessHours || ''
								};
							}),
						...Object.entries(dayMetrics.outsideBusinessHours[selectedMetrics.planned])
							.filter((item) => {
								if (selectedLegend?.planned?.outsideBusinessHours?.length)
									return selectedLegend.planned.outsideBusinessHours.includes(item[0]);

								if (selectedLegend.planned.withinBusinessHours?.length) return null;

								return item;
							})
							.map(([metric, total]) => {
								return {
									metric,
									total,
									color: metricColors.planned[selectedMetrics.planned][metric]?.outsideBusinessHours || ''
								};
							})
					]
				: [];
		};

		if (dayMetrics.withinBusinessHours && dayMetrics.outsideBusinessHours) {
			if (isToday) {
				setTodayPlannedAppointments(getPlannedTabStand(dayMetrics));
			}
			if (datePast) {
				tabStand = Object.entries(dayMetrics.withinBusinessHours[selectedMetrics.done])
					.filter((item) => (selectedLegend?.done?.length ? selectedLegend.done.includes(item[0]) : item))
					.map(([metric, total]) => {
						const totalOutsideBusinessHours = dayMetrics.outsideBusinessHours?.[selectedMetrics.done][metric];
						return {
							metric,
							total: totalOutsideBusinessHours ? total + totalOutsideBusinessHours : total,
							color: metricColors.done[selectedMetrics.done][metric] || ''
						};
					});
			} else {
				tabStand.push(...getPlannedTabStand(dayMetrics));
			}
		}
		setFilteredAppointmentsByState(tabStand);
	}, [selectedLegend, datePast, dayMetrics.outsideBusinessHours, dayMetrics.withinBusinessHours, selectedMetrics, dayMetrics, isToday]);

	const total = isToday
		? Math.max(
				filteredAppointmentsByState?.map((item) => item.total).reduce((acc, curr) => acc + curr, 0) || 0,
				todayPlannedAppointments?.map((item) => item.total).reduce((acc, curr) => acc + curr, 0) || 0
			)
		: filteredAppointmentsByState?.map((item) => item.total).reduce((acc, curr) => acc + curr, 0);

	useEffect(() => {
		setHeight(total ? (total / maxDailyAppointments) * 100 : 0);
	}, [total, maxDailyAppointments, filteredAppointmentsByState]);

	const hnoTotal =
		dayMetrics.outsideBusinessHours &&
		datePast &&
		Object.entries(dayMetrics.outsideBusinessHours[selectedMetrics.done])
			.filter((item) => (selectedLegend?.done?.length ? selectedLegend.done.includes(item[0]) : item))
			.reduce((accumulator, currentValue) => accumulator + currentValue[1], 0);

	return (
		<button
			className={clsx('flex h-full w-full flex-col items-center justify-end rounded-tl-2xl px-0.5')}
			onClick={() => setSelectedDay(dayMetrics.day)}>
			<div
				className='flex items-end justify-center w-full'
				style={{
					height: `${(100 - height) * 0.8}%`
				}}>
				{isToolTip && (
					<div className='flex flex-col items-center p-2 px-2 mb-2 space-y-1 bg-white rounded-md shadow-md shadow-slate-500/10'>
						<div className='text-xs whitespace-nowrap text-slate-400'>
							{format(dayMetrics.day, 'dd MMMM yyyy', { locale: fr })}
						</div>
						<div className='text-sm font-semibold whitespace-nowrap'>{`${total} intervention${total ? 's' : ''}`}</div>
					</div>
				)}
				{!!hnoTotal && !isToolTip && !isToday && (
					<div className='flex flex-col items-center mb-2'>
						<div className='flex items-center justify-center rounded-xl bg-gray-300 px-2 py-[2px] text-xs font-semibold shadow-md shadow-slate-500/10'>
							{hnoTotal}
						</div>
					</div>
				)}
			</div>
			{filteredAppointmentsByState ? (
				<div
					className={clsx(
						'flex w-full cursor-pointer flex-col items-center justify-center rounded-t-md transition-[height] duration-1000'
					)}
					style={{
						height: `${height > 5 && total ? height * 0.8 : 3}%`
					}}>
					<div
						className={clsx(
							'flex h-full w-full origin-bottom transform animate-anim_bar space-x-[4px] rounded-t-md border-blue-500 transition-all'
						)}
						style={{
							borderWidth: selectedDay && isSameDay(selectedDay, dayMetrics.day) ? '3px' : '0px'
						}}>
						<BarDay
							tabStand={filteredAppointmentsByState}
							total={total || 0}
							height={height}
							isToday={isToday}
							datePast={datePast}
						/>
						{isToday && todayPlannedAppointments && (
							<BarDay tabStand={todayPlannedAppointments} total={total || 0} height={height} isToday={isToday} />
						)}
					</div>
				</div>
			) : null}
		</button>
	);
}
