import { useEffect } from 'react';
// tanstack
import { keepPreviousData, useQueries } from '@tanstack/react-query';
// utils
import { addDays, format } from 'date-fns';
// api
import type { IFiberAppointment } from '@/api/interface/fiber';
import type { IOrderItem } from '@/api/interface/fiber';
import { queries } from '@/api/queries';
// sections
import FilterBar from './filter-bar';
import HeaderTable from './header-table';
import HoursSelect from './hours-select';
import TableOrderAppointment from './table-order-appointment/table-order-appointment';
import { useAppointmentsFilter } from '../../_hooks/use-appointments-filters';
import { useAppointmentsSelectedDay } from '@/sections/universe/fiber/interventions/_hooks/atoms/use-appointments-selected-day';
// components
import Skeleton from '@/components-old/reactore/tou-migrate/skeleton';
import { usePaginationV2 } from '@/components-old/ui/pagination/pagination-v2.hooks';

// ----------------------------------------------------------------------

export const minWidthTab: TInfoLine = {
	typeRdv: 'min-w-[300px] h-[20px]',
	hRdv: 'min-w-[135px] h-[20px]',
	dRdv: 'min-w-[180px] h-[20px]',
	state: 'min-w-[180px] h-[20px]',
	jeton: 'min-w-[190px] h-[20px]',
	company: 'min-w-[180px] h-[20px]',
	cp: 'min-w-[130px] h-[20px]',
	upr: 'min-w-[180px] h-[20px]'
};

export type TInfoLine = {
	typeRdv?: string;
	hRdv?: string | Date;
	dRdv?: string;
	state?: string;
	jeton?: string;
	migrate?: boolean;
	company?: string;
	cp?: string;
	upr?: string;
	hRdvBrut?: string;
};

export default function TableAppointmentContainer() {
	const [selectedDay] = useAppointmentsSelectedDay();
	const { setAppoints } = useAppointmentsFilter();

	const porpsPag = usePaginationV2<IFiberAppointment>({
		queryKey: queries.fiber_appointment.all,
		// numberTickets: watchNumberSelect.numberSelect,
		params: {
			'createdAt[before]': selectedDay && addDays(selectedDay, 1),
			'createdAt[after]': selectedDay,
			itemsPerPage: 20
		},
		paramQuery: { placeholderData: keepPreviousData }
	});
	const { data: appointmentsPerPage, queryPagination } = porpsPag;

	const appointmentsOrdersQueries = useQueries({
		queries: appointmentsPerPage
			? appointmentsPerPage.map((appointment) => ({
					...queries.fiber_order.byId(appointment.order),
					enabled: Boolean(appointmentsPerPage.length && appointment.order)
				}))
			: []
	});

	const isSuccessAppointmentsOrdersQueries = appointmentsOrdersQueries.every((currentdata) => currentdata.isSuccess);

	useEffect(() => {
		if (isSuccessAppointmentsOrdersQueries && appointmentsPerPage) {
			const tableAppointments = appointmentsPerPage.reduce<TInfoLine[]>((acc, curr) => {
				const appointmentOrder: IOrderItem | undefined = appointmentsOrdersQueries.find(
					(order) => order.data?.['@id'] === curr.order
				)?.data;

				return acc.concat({
					company: appointmentOrder?.company?.name,
					cp: appointmentOrder?.endpointAddress?.postalCode,
					dRdv:
						(curr.appointmentSchedules?.[0]?.planedDuration && curr.appointmentSchedules?.[0]?.planedDuration.toString()) || '',
					hRdv: curr.appointmentSchedules?.[0]?.scheduledAt
						? format(new Date(curr.appointmentSchedules[0].scheduledAt), 'H:mm')
						: '',
					jeton: appointmentOrder?.reference,
					migrate: appointmentOrder?.currentOis?.rops[0]?.isMigration,
					state: appointmentOrder?.state,
					typeRdv: appointmentOrder?.appointments[0]?.type,
					// TODO: maj par rapport à l'api
					upr: Math.floor(Math.random() * 18).toString()
				});
			}, []);

			setAppoints(tableAppointments);
		}
	}, [isSuccessAppointmentsOrdersQueries, appointmentsPerPage]);

	if (appointmentsOrdersQueries.some((order) => order.isPending)) {
		return <Skeleton className='w-full h-full rounded-2xl' />;
	}

	if (appointmentsPerPage.length) {
		return (
			<div className='flex flex-col w-full h-full'>
				<div className='flex h-full w-full flex-col rounded-2xl border-[2px] px-6 dark:border-free-dark--smoke-light dark:bg-free-dark-smoke dark:text-gray-200'>
					<div className='flex items-start justify-between mt-4 mb-10'>
						<div className='flex-col'>
							{selectedDay && <HoursSelect selectedDay={selectedDay} />}
							<p className='text-[35px] font-bold'>
								{`${queryPagination.data?.['hydra:totalItems']} intervention${
									queryPagination.data?.['hydra:totalItems'] && queryPagination.data?.['hydra:totalItems'] > 1 ? 's' : ''
								}`}
							</p>
						</div>
						<HeaderTable />
					</div>
					<FilterBar />
					<div className='flex-1 py-4 pt-0 overflow-x-auto'>
						<TableOrderAppointment />
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className='flex flex-col w-full h-full'>
			<div className='flex h-full w-full flex-col rounded-2xl border-[2px] px-6 dark:border-free-dark--smoke-light dark:bg-free-dark-smoke dark:text-gray-200'>
				<div className='flex items-center justify-center h-full mt-4 mb-10'>
					<p>0 intervention</p>
				</div>
			</div>
		</div>
	);
}
