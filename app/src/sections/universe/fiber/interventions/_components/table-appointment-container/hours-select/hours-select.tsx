// utils
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
// sections
import { useAppointmentsFilter } from '../../../_hooks/use-appointments-filters';

// ----------------------------------------------------------------------

export default function HoursSelect({ selectedDay }: { selectedDay: Date }) {
	const { hrs } = useAppointmentsFilter();
	return (
		<div className='flex space-x-2'>
			<div className='text-slate-400'>{`${format(selectedDay, 'dd MMMM yyyy', { locale: fr })}`}</div>
			{hrs > -1 && (
				<div className='flex space-x-1'>
					<div className='font-semibold text-slate-500'>- à {`${hrs}h00`}</div>
				</div>
			)}
		</div>
	);
}
