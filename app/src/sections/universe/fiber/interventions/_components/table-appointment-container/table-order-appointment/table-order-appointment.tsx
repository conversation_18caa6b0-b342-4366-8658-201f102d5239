import { useEffect, useRef, useState } from 'react';
// utils
import { Typography, useOnClickOutside } from '@tools/reactore';
import { twJoin } from 'tailwind-merge';
// hooks
import { useDragTab } from '@/hooks/use-drag-tab.hooks';
// sections
import { type TPropColumn, useAppointmentsFilter } from '../../../_hooks/use-appointments-filters';
import { HoursD, HoursR, Maj, Order, State, TypeR, Upr } from './module/module';
// components
import { ChevronUpIcon, ChevronDownIcon, ExchangeVIcon, SixPointVerticalIcon } from '@/components-old/ui/icon';

// ----------------------------------------------------------------------

const nodeChil = {
	typeRdv: TypeR,
	hRdv: HoursR,
	dRdv: HoursD,
	state: State,
	jeton: Order,
	company: Maj,
	cp: Maj,
	upr: Upr
};

const isExchange = {
	state: true,
	jeton: true,
	company: true,
	cp: true
};

type TColumnProps = { data: TPropColumn; ind: string };

function Column({ data, ind }: TColumnProps) {
	const NodeElem = nodeChil[ind];

	return (
		// eslint-disable-next-line react/jsx-no-useless-fragment
		<>
			{data &&
				data.map(({ ...obj }, inds) => {
					return <NodeElem key={`key${inds}`} {...obj} />;
				})}
		</>
	);
}

type TTreeTypeProps = { handleExchange: (nb: number) => void; id: string };

function TreeType({ handleExchange, id }: TTreeTypeProps) {
	const ref = useRef(null);
	const handleOutSide = () => {
		handleExchange(-1);
	};
	useOnClickOutside(ref, handleOutSide);

	const { dispatchFilter } = useAppointmentsFilter();

	return (
		<div ref={ref} className='absolute z-[200]'>
			<div className='flex flex-col rounded-xl bg-white p-2 shadow-lg dark:border-[1px] dark:border-free-dark--smoke-light dark:bg-free-dark-smoke'>
				<Typography className='px-1 py-2 text-xs' color='gray'>
					Trier par
				</Typography>
				<button
					className='flex items-center p-1 pl-2 rounded-md cursor-pointer hover:bg-slate-100'
					onClick={() => dispatchFilter({ type: 'sortAscending', action: id })}>
					<ChevronUpIcon className=' mr-2 w-[10px] text-gray-400' />
					<Typography className='text-sm whitespace-nowrap'>Trier par ordre croissant</Typography>
				</button>
				<button
					className='flex items-center p-1 pl-2 rounded-md cursor-pointer hover:bg-slate-100'
					onClick={() => dispatchFilter({ type: 'sortDescending', action: id })}>
					<ChevronDownIcon className='mr-2 w-[10px] text-gray-400' />
					<Typography className='text-sm whitespace-nowrap'>Trier par ordre decroissant</Typography>
				</button>
			</div>
		</div>
	);
}
type TTitleProps = { ind: number; id: string };

const type = {
	typeRdv: 'Type de RDV',
	hRdv: 'Heure du RDV',
	dRdv: 'Durée du RDV',
	state: 'Etat',
	jeton: 'Jeton de commande',
	company: 'Entreprise',
	cp: 'Code Postal',
	upr: 'UPR'
};

function Title({ ind, id }: TTitleProps) {
	const [treeExchange, setTreeExchange] = useState(-1);
	const [isOver, setIsOver] = useState(false);
	const [title, setTitle] = useState();
	const [isDataExchange, setsDataExchange] = useState(false);

	useEffect(() => {
		setTitle(type[id]);
		setsDataExchange(isExchange[id]);
	}, [id]);

	const getStyles = (isOver: boolean) => {
		return isOver ? 'bg-slate-300/20' : 'bg-whith/100';
	};
	const getStylesHidden = (isOver: boolean) => {
		return isOver ? 'text-slate-400/100' : 'text-slate-400/0';
	};
	return (
		<div
			className={twJoin(
				'-ml-4 flex w-[calc(100%_+_15px)] items-center justify-between rounded-xl px-4 transition-all',
				getStyles(isOver)
			)}
			onMouseEnter={(): void => {
				setIsOver(true);
			}}
			onMouseLeave={(): void => {
				setIsOver(false);
			}}
			role='dialog'>
			<div className='flex items-center justify-center pr-2'>
				<div className='pr-2 text-xs text-gray-600 text-ellipsis whitespace-nowrap'>{title}</div>
				{isDataExchange && (
					<div className='relative cursor-pointer z-60' onClick={() => setTreeExchange(ind)} aria-hidden='true'>
						<ExchangeVIcon className='text-gray-500 cursor-pointer ' />
						{treeExchange === ind && <TreeType handleExchange={(nb: number) => setTreeExchange(nb)} id={id} />}
					</div>
				)}
			</div>
			<div className='w-2'>
				<SixPointVerticalIcon className={twJoin('w-[100%] text-slate-400', getStylesHidden(isOver))} />
			</div>
		</div>
	);
}

type TPrevisualProps = { select: number[]; hid: boolean; ind: number };

function Previsual({ select, hid, ind }: TPrevisualProps) {
	return hid ? (
		<div
			className='h-full rounded-xl bg-gray-50 transition-[width] duration-500 ease-in-out'
			style={{
				width: select[0] === ind ? `${select[1]}px` : '0px',
				height: select[0] === ind ? `${select[2]}px` : '0px'
			}}
		/>
	) : null;
}

export default function TableOrderAppointment() {
	const { data: dataFilter, setFilter } = useAppointmentsFilter();

	const handleFilter = (newList: any) => {
		setFilter(newList.map((el: { id: string }) => el.id));
	};
	const { selectId, drag, dragStart, dragEnter, dragEnd } = useDragTab(dataFilter as [], handleFilter);

	return (
		<table className='flex items-start' onDragOver={(e) => e.preventDefault()}>
			{dataFilter.map(({ id, data }, ind) => {
				return (
					<div
						className={twJoin(`relative flex w-full select-none`)}
						style={{ zIndex: dataFilter ? dataFilter.length - ind : '0' }}
						key={id}>
						<Previsual ind={ind} hid={ind < drag.current} select={selectId} />
						<div
							className='flex flex-col items-start justify-between w-full p-2 space-y-4 translate-x-0 translate-y-0'
							onDragStartCapture={(e: any) => {
								dragStart(e, ind);
							}}
							role='dialog'
							draggable
							onDragEnter={() => dragEnter(ind)}
							onDragEnd={dragEnd}>
							<div className={twJoin('flex h-8 w-full translate-x-0 translate-y-0 cursor-grab justify-between')}>
								<Title ind={ind} id={id} />
							</div>
							<Column data={data} ind={id} />
						</div>
						<Previsual ind={ind} hid={ind > drag.current} select={selectId} />
					</div>
				);
			})}
		</table>
	);
}
