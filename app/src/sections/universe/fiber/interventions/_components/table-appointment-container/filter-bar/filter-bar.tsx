import { useState } from 'react';
// utils
import { Input } from '@tools/reactore';
// sections
import { useAppointmentsFilter } from '../../../_hooks/use-appointments-filters';
// components
import InputSelectCustom from '@/components-old/reactore/input-select-custom';
import type { TOptionListSelect } from '@/components-old/reactore/input-select-custom/input-select-custom.interface';
import { FilterIcon } from '@/components-old/ui/icon';
// others
import type { TOptionList } from '@/components/reactore/Input-select/input-select.interface';

// ----------------------------------------------------------------------

const inputUpr: TOptionList[] = [
	{ label: 'Aucun', value: '' },
	{ label: '00', value: '00' },
	{ label: '01', value: '01' },
	{ label: '02', value: '02' },
	{ label: '03', value: '03' },
	{ label: '04', value: '04' },
	{ label: '05', value: '05' },
	{ label: '06', value: '06' },
	{ label: '07', value: '07' },
	{ label: '08', value: '08' },
	{ label: '09', value: '09' },
	{ label: '10', value: '10' },
	{ label: '11', value: '11' },
	{ label: '12', value: '12' },
	{ label: '13', value: '13' },
	{ label: '14', value: '14' },
	{ label: '15', value: '15' },
	{ label: '16', value: '16' },
	{ label: '17', value: '17' }
];

export default function FilterBar() {
	const [isOpen, setIsOpen] = useState(false);
	const { multiSelect, dispatchFilter, upr } = useAppointmentsFilter();

	const handleChangeMult = (el: TOptionListSelect | undefined, value?: boolean) => {
		if (el) dispatchFilter({ type: 'sortMultiSelect', action: el });
		else if (typeof value === 'boolean') dispatchFilter({ type: 'sortMultiSelectAll', action: value });
	};

	const handleChangeUpr = (value: string) => {
		dispatchFilter({ type: 'sortUpr', action: value });
	};

	return (
		<div className=' flex w-full flex-col items-center justify-center'>
			<div className='mb-2 flex w-full justify-between rounded-lg'>
				<div className='' />
				<button
					className='flex cursor-pointer items-center justify-center rounded-lg px-2 py-1 hover:bg-slate-100 dark:hover:bg-free-dark-smoke-light'
					onClick={() => setIsOpen((el) => !el)}>
					<FilterIcon className='w-5 text-gray-500' />
					<div className='font-medium text-gray-600 dark:text-gray-400'>Filtres</div>
				</button>
			</div>
			<div
				className='w-full transition-all duration-200'
				style={{
					height: isOpen ? '80px' : '0px',
					overflowY: !isOpen ? 'auto' : 'visible'
				}}>
				<div className='flex w-full items-start justify-between space-x-4'>
					<Input
						label='UPR'
						type='select'
						value={upr}
						selectOptions={{ items: inputUpr }}
						name='upr'
						size='md'
						className='flex w-full flex-1 dark:rct-input:border-free-dark--smoke-light dark:rct-input:bg-free-dark-smoke dark:rct-input/label:text-gray-200 dark:rct-input/select:border-free-dark--smoke-light dark:rct-input/select:bg-free-dark-smoke'
						onChange={(e) => {
							if (e.target.value !== '[object Object]') {
								handleChangeUpr(e.target.value);
							} else {
								handleChangeUpr('');
							}
						}}
					/>
					<div className='w-3/4'>
						<InputSelectCustom
							// label='Type de rendez vous' TODO: a revoir
							optionList={multiSelect}
							handleChange={handleChangeMult}
							placeholder='Label'
							// noWrap TODO: a revoir
						/>
					</div>
				</div>
			</div>
		</div>
	);
}
