// utils
import { format, parse } from 'date-fns';
import { atom, useAtom } from 'jotai';
import { atomWithStorage } from 'jotai/utils';
// sections
import { type TInfoLine, minWidthTab } from '../_components/table-appointment-container/table-appointment-container';
// components
import type { TOptionListSelect } from '@/components-old/reactore/input-select-custom/input-select-custom.interface';

// ----------------------------------------------------------------------

const appointments = atom<TInfoLine[]>([]);

const filterColumnAtom = atomWithStorage<TListColumn>('filterColumnAppointement', [
	'typeRdv',
	'hRdv',
	'dRdv',
	'state',
	'jeton',
	'company',
	'cp',
	'upr'
]);

const filterMultiSelect = atomWithStorage<TOptionListSelect[]>('filterMultiSelect', [
	{
		value: true,
		label: 'analyse'
	},
	{
		value: true,
		label: 'previsite'
	},
	{
		value: true,
		label: 'raccordement'
	},
	{
		value: true,
		label: 'post_production'
	},
	{
		value: true,
		label: 'devis'
	},
	{
		value: true,
		label: 'travaux'
	},
	{
		value: true,
		label: 'sans_contact'
	},
	{
		value: true,
		label: 'support'
	}
]);

export type TAscending = {
	isAscending: boolean;
	filter: string;
};
export type TUpr = string;

const uprFilter = atomWithStorage<TUpr>('uprFilter', '');
const sortHours = atomWithStorage<number>('sortHours', -1);

const ascendingFilter = atomWithStorage<TAscending>('ascendingFilter', {
	isAscending: true,
	filter: ''
});
export type TListColumn = [string, string, string, string, string, string, string, string];
const sortData = (data: TInfoLine[], type: TListColumn) => {
	return type.map((typeEl) => {
		return data.map((dataCol) => {
			return { [typekey[typeEl]]: dataCol[typeEl], className: minWidthTab[typeEl] };
		});
	});
};
const formatData = (dataTab: TInfoLine[], filter: TListColumn): TDataColumn[] => {
	const tabTree = sortData(dataTab, filter);
	return filter.map((el: string, ind: number) => {
		return { id: el, data: tabTree[ind] };
	});
};
const sortAscending = (id: string, data: TInfoLine[], filter: TListColumn) => {
	const dt = data
		.sort((a: object, b: object) => {
			if (b[id] > a[id]) return 1;
			if (a[id] > b[id]) return -1;
			return 0;
		})
		.reverse();
	return formatData(dt, filter);
};
const sortDescending = (id: string, data: TInfoLine[], filter: TListColumn) => {
	const dt = data.sort((a: object, b: object) => {
		if (b[id] > a[id]) return 1;
		if (a[id] > b[id]) return -1;
		return 0;
	});
	return formatData(dt, filter);
};
const sortMultiSelect = (data: TInfoLine[], filter: TListColumn, tb: TOptionListSelect[]) => {
	let ttb: TInfoLine[] = [];
	tb.forEach(({ label, value }) => {
		ttb = [
			...ttb,
			...(data.filter((el) => {
				if (value && label === el.typeRdv) return true;
				return false;
			}) as TInfoLine[])
		];
	});

	return formatData(ttb, filter);
};

const sortUpr = (id: string, data: TInfoLine[], filter: TListColumn) => {
	const dt = data.filter(({ upr }) => {
		return upr === id;
	});
	return formatData(dt, filter);
};

export type TFilter = {
	multiSelect: TOptionListSelect[];
	data: TDataColumn[];
	setFilter: (update: TListColumn) => void;
	setAppoints: (update: TInfoLine[]) => void;
	dispatchFilter: ({ type, action }: { type: string; action: any }) => void;
	upr: string;
	appoints: TInfoLine[];
	hrs: number;
};
export type TPropColumn =
	| {
			[x: string]: any;
			className: any;
	  }[]
	| undefined;
export type TDataColumn = {
	id: string;
	data: TPropColumn;
};

const convertTab = (tb: TDataColumn[]) => {
	const data = tb[0] as TDataColumn;

	const dataObj = data.data;

	const length = dataObj !== undefined ? dataObj.length : 0;

	let dataTb: TInfoLine[] = [];
	for (let i = 0; i < length; i++) {
		let obj = {};
		tb.forEach(({ id: str }) => {
			const ob = tb.find((el) => el.id === str) as TDataColumn;
			if (ob !== undefined) {
				const obData = ob?.data;
				if (obData !== undefined) {
					const dataStr = obData[i];
					if (dataStr !== undefined) obj = { ...obj, [str]: dataStr.str };
				}
			}
		});
		dataTb = [...dataTb, obj as TInfoLine];
	}
	return dataTb;
};

const sortHour = (hrs: number, data: TInfoLine[], filter: TListColumn) => {
	return hrs > -1
		? formatData(
				data.filter(({ hRdv }) => hRdv && hrs === Number(format(parse(hRdv as string, 'HH:mm', new Date()), 'kk'))),
				filter
			)
		: formatData(data, filter);
};

export const useAppointmentsFilter = (): TFilter => {
	const [appoints, setAppoints] = useAtom(appointments);
	const [filter, setFilter] = useAtom(filterColumnAtom);
	const [multiSelect, setMultiSelect] = useAtom(filterMultiSelect);
	const [ascending, setAscending] = useAtom(ascendingFilter);
	const [upr, setUpr] = useAtom(uprFilter);
	const [hrs, setHrs] = useAtom(sortHours);

	const handlerAppoints = (update: TInfoLine[]) => {
		setAppoints(update);
	};
	const handlerFilter = (filt: TListColumn) => {
		setFilter(filt);
	};

	const reduceAllFilters = () => {
		let newTb = [];
		newTb = sortMultiSelect(appoints, filter, multiSelect);
		if (upr !== '') newTb = sortUpr(upr, convertTab(newTb), filter);
		if (ascending.isAscending) newTb = sortAscending(ascending.filter, convertTab(newTb), filter);
		else newTb = sortDescending(ascending.filter, convertTab(newTb), filter);
		newTb = sortHour(hrs, convertTab(newTb), filter);

		return newTb;
	};

	const reducer = ({ type, action }: { type: string; action: any }) => {
		switch (type) {
			case 'sortAscending':
				setAscending(() => ({
					isAscending: true,
					filter: action
				}));
				break;
			case 'sortDescending':
				setAscending(() => ({
					isAscending: false,
					filter: action
				}));
				break;
			case 'sortMultiSelect': {
				const tbMult = multiSelect.map(({ label, value }) => {
					if (label === action.label) return { label, value: !value };
					return { label, value };
				});
				setMultiSelect(tbMult);
				break;
			}
			case 'sortMultiSelectAll': {
				const tbMultAll = multiSelect.map(({ label }) => ({ label, value: action }));
				setMultiSelect(tbMultAll);
				break;
			}
			case 'sortUpr':
				setUpr(action);
				break;
			case 'sortHours':
				setHrs(action);
				break;
			default:
				break;
		}
	};
	return {
		multiSelect,
		data: reduceAllFilters(),
		setFilter: handlerFilter,
		setAppoints: handlerAppoints,
		appoints,
		dispatchFilter: reducer,
		upr,
		hrs
	};
};

const typekey: TInfoLine = {
	typeRdv: 'str',
	hRdv: 'str',
	dRdv: 'str',
	state: 'state',
	jeton: 'str',
	company: 'str',
	cp: 'str',
	upr: 'str'
};
