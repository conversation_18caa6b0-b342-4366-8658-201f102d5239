// utils
import { getColorStateOrder } from '@/utils';
import { capitalizeCase } from '@/utils/change-case';
import { Tag } from '@tools/reactore';
import { differenceInCalendarDays, formatDate } from 'date-fns';
// api
import { parseIri } from '@/api/fetcher';
import { EFiberOrderState, type IOrderItem } from '@/api/interface';
// sections
import { ReferenceFormater } from '@/sections/_components';
// components
import { OrderTypeFreeProIcon, OrderTypeJaguarNetworkIcon, OrderTypeProteamIcon, OrderTypeWholesaleIcon } from '@/components-old/ui/icon';
import { DataTableColumnHeader } from '@/components/custom-table';
import { type ExtendedColumnDef, handleInitColumnsDataByKey } from '@/components/custom-table/data-table-dnd';
import { Checkbox } from '@/components/ui/checkbox';

// ----------------------------------------------------------------------

const columns: ExtendedColumnDef<IOrderItem>[] = [
	{
		id: 'select',
		accessorKey: 'select',
		title: 'Select',
		header: ({ table }) => (
			<Checkbox
				checked={table.getIsAllPageRowsSelected()}
				onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
				aria-label='Select all'
				className='translate-y-[2px]'
			/>
		),
		cell: ({ row }) => (
			<Checkbox
				checked={row.getIsSelected()}
				onCheckedChange={(value) => row.toggleSelected(!!value)}
				aria-label='Select row'
				className='translate-y-[2px]'
			/>
		),
		enableSorting: false,
		enableHiding: false
	},
	{
		accessorKey: 'retailer',
		id: 'retailer',
		title: 'Retailer',
		header: 'Retailer',
		cell: ({ row }) => {
			const retailer = row.getValue('retailer') as string;

			if (!retailer) {
				return null;
			}

			switch (parseIri(retailer as string)) {
				case '1':
					return <OrderTypeFreeProIcon className='h-6 text-white dark:text-gray-200' />;
				case '2':
					return <OrderTypeJaguarNetworkIcon className='h-6 text-white dark:text-gray-200' />;
				case '3':
					return <OrderTypeWholesaleIcon className='h-6 text-white dark:text-gray-200' />;
				case '4':
					return <OrderTypeProteamIcon className='h-6 text-white dark:text-gray-200' />;
				default:
					return null;
			}
		}
	},
	{
		accessorKey: 'reference',
		id: 'reference',
		header: 'Jeton',
		title: 'Jeton',
		cell: ({ row }) => {
			const reference = row.getValue('reference') as string;
			return <ReferenceFormater str={reference} className='text-sm' />;
		}
	},
	{
		id: 'state',
		accessorKey: 'state',
		title: 'Statut',
		header: ({ column }) => <DataTableColumnHeader column={column} title='Statut' />,
		cell: ({ row }) => {
			const rowState = row.getValue('state');
			const state = EFiberOrderState[rowState as keyof typeof EFiberOrderState];
			const color = getColorStateOrder(rowState as EFiberOrderState);
			return <Tag className={color}>{capitalizeCase(state)}</Tag>;
		}
	},
	{
		id: 'company',
		title: 'Entreprise',
		accessorKey: 'company',
		header: ({ column }) => <DataTableColumnHeader column={column} title='Entreprise' />,
		cell: ({ row }) => {
			const company = row.getValue('company') as { name: string };
			return company?.name;
		}
	},
	{
		id: 'endpointAddress',
		title: 'Adresse',
		accessorKey: 'endpointAddress',
		header: ({ column }) => <DataTableColumnHeader column={column} title='Adresse' />,
		cell: ({ row }) => {
			const endpointAddress = row.getValue('endpointAddress') as {
				street: string;
				street2: string;
				postalCode: string;
				city: string;
			};

			const address = `${endpointAddress?.street ?? ''} ${endpointAddress?.street2 ?? ''} ${endpointAddress?.postalCode ?? ''} ${endpointAddress?.city ?? ''}`;

			return address;
		}
	},
	{
		id: 'createdAt',
		title: 'Créé le',
		accessorKey: 'createdAt',
		header: ({ column }) => <DataTableColumnHeader column={column} title='Créé le' />,
		cell: ({ row }) => {
			const createdAt = row.getValue('createdAt') as string;
			const diffday = (dateSelect: string) => {
				const diffDays = differenceInCalendarDays(new Date(), dateSelect);
				switch (diffDays) {
					case 0:
						return `Aujourd'hui à ${formatDate(dateSelect, "HH'h'mm")}`;
					case 1:
						return `Hier à ${formatDate(dateSelect, "HH'h'mm")}`;
					default:
						return formatDate(dateSelect, "dd/MM/yyyy à HH'h'mm");
				}
			};
			return diffday(createdAt);
		}
	},
	{
		id: 'updatedAt',
		title: 'Mis à jour le',
		accessorKey: 'updatedAt',
		header: ({ column }) => <DataTableColumnHeader column={column} title='Mis à jour le' />,
		cell: ({ row }) => {
			const updatedAt = row.getValue('updatedAt') as string;
			const diffday = (dateSelect: string) => {
				const diffDays = differenceInCalendarDays(new Date(), dateSelect);
				switch (diffDays) {
					case 0:
						return `Aujourd'hui à ${formatDate(dateSelect, "HH'h'mm")}`;
					case 1:
						return `Hier à ${formatDate(dateSelect, "HH'h'mm")}`;
					default:
						return formatDate(dateSelect, "dd/MM/yyyy à HH'h'mm");
				}
			};
			return diffday(updatedAt);
		}
	},
	{
		id: 'customerReference',
		title: 'Client Référence',
		accessorKey: 'customerReference',
		header: 'Client Référence',
		cell: ({ row }) => {
			const reference = row.getValue('customerReference') as string;
			return <ReferenceFormater str={reference} className='text-sm' />;
		}
	},
	{
		id: 'wholesaleReference',
		title: 'Référence du grossiste',
		accessorKey: 'wholesaleReference',
		header: 'Référence du grossiste',
		cell: ({ row }) => {
			const reference = row.getValue('wholesaleReference') as string;
			return <ReferenceFormater str={reference} className='text-sm' />;
		}
	}
];

export { columns };

export const columnsDataByKey = handleInitColumnsDataByKey(columns);
