// tanstack
import { useQuery } from '@tanstack/react-query';
import { useParams } from '@tanstack/react-router';
// utils
import { ApiErrors, Typography } from '@tools/reactore';
// api
import { queries } from '@/api/queries';
// sections
import OiHistoryTableRow from './oi-history-table-row';
// components
import Skeleton from '@/components-old/reactore/tou-migrate/skeleton';
import NotAvailable from '@/components-old/ui/not-available';

// ----------------------------------------------------------------------

function OIHistory() {
	const { id } = useParams({ strict: false });

	const { data: ois, error: oisError } = useQuery({
		...queries.fiber_order.byId(id as string)._ctx.ois,
		enabled: <PERSON><PERSON><PERSON>(id)
	});

	if (ois) {
		return (
			<section className='col-span-1'>
				<div className='flex h-full flex-col'>
					<Typography type='h5' fontFamily='iliad' className='mb-2 text-xl font-normal text-gray-400 dark:text-white'>
						Historique OI
					</Typography>
					<div className='card h-full rounded-2xl p-6'>
						{ois?.['hydra:member'].length ? (
							<table className='table-base'>
								<thead>
									<tr className='child:color-gray child:caption child:font-light'>
										<th>ID</th>
										<th>Dernier ETA non final</th>
										<th>Date creation</th>
										<th>Dernière mise à jour</th>
										<th>Etat</th>
									</tr>
								</thead>
								<tbody className='body-sm child:color-normal'>
									{ois?.['hydra:member'].map((oi) => (
										<OiHistoryTableRow oi={oi} />
									))}
								</tbody>
							</table>
						) : (
							<NotAvailable />
						)}
					</div>
				</div>
			</section>
		);
	}

	if (oisError) {
		return (
			<section className='col-span-1'>
				<div className='flex h-full flex-col'>
					<Typography type='h5' fontFamily='iliad' className='mb-2 text-xl font-normal text-gray-400 dark:text-white'>
						Historique OI
					</Typography>
					<div className='card h-full p-6'>
						<ApiErrors errors={[oisError]} />
					</div>
				</div>
			</section>
		);
	}

	return (
		<section className='col-span-1'>
			<div className='flex h-full flex-col'>
				<Typography type='h5' fontFamily='iliad' className='mb-2 text-xl font-normal text-gray-400 dark:text-white'>
					Historique OI
				</Typography>
				<Skeleton className='h-64 rounded-2xl' />
			</div>
		</section>
	);
}

export default OIHistory;
