// tanstack
import { useQuery } from '@tanstack/react-query';
// api
import type { IFiberHistory } from '@/api/interface/fiber';
import type { IFiberNetwork } from '@/api/interface/fiber';
import type { IOrderItem } from '@/api/interface/fiber';
import type { IFiberWorkflowDetails } from '@/api/interface/fiber';
import { queries } from '@/api/queries';
// hooks
import { useOrder } from '@/hooks/queries/use-order';

// ----------------------------------------------------------------------

const getNetworkCodeName = (state: string, networkHistory: IFiberWorkflowDetails, order: IOrderItem, currentAppointment: IFiberHistory) => {
	const stateDetails = networkHistory?.states.find((item) => item.toState === state);
	const technology = order?.currentOis?.technology;

	const getCode = () => {
		switch (state) {
			case 'OT_CREATED':
				if (order?.currentOis?.isMigration && order.source === '1G') {
					return {
						code: '864',
						name: 'CABLAGE_P2P_WAIT_BRK'
					};
				}
				if (stateDetails?.fromState === 'WAITING_MIGRATION') {
					return {
						code: '634',
						name: 'INIT_MIGRATION'
					};
				}

				return {
					code: '550',
					name: 'NRO_WAIT'
				};
			case 'WAITING_MIGRATION':
				return {
					code: '634',
					name: 'WAITING_MIGRATION'
				};
			case 'SENT':
				return {
					code: '801',
					name: 'FIBRE_DEMANDEE'
				};
			case 'ACKNOWLEDGED':
				return {
					code: '802',
					name: 'FIBRE_A_CABLER'
				};
			case 'POSITIONED':
				if (!order?.currentOnus) {
					return { code: '1100', name: 'MAC_ATTENTE_AJOUT' };
				}
				if (technology === 'PON_CAD') {
					return {
						code: '852',
						name: 'CABLAGE_PON_OK'
					};
				}
				if (technology === 'PON_LNP') {
					return {
						code: '852',
						name: 'CABLAGE_LNP_OK'
					};
				}

				return {
					code: '803',
					name: 'CABLAGE_COMMANDE'
				};
			case 'COMPLETED':
				if ((technology === 'PON_CAD' || technology === 'PON_LNP') && stateDetails?.fromState === 'ACTIVATION_ACKNOWLEDGED') {
					if (technology === 'PON_CAD') {
						return {
							code: '856',
							name: 'DECABLAGE_PON_OK'
						};
					}
					if (technology === 'PON_LNP') {
						return { code: '858', name: 'DECABLAGE_LNP_OK' };
					}
				}
				if (stateDetails?.fromState === 'RESERVATION_NOTIFIED') {
					if (order?.currentNetworks?.cablingSpool?.activation?.notifiedAt) {
						return {
							code: '896',
							name: 'DECAB_CR_OK'
						};
					}
				}

				return {
					code: '898',
					name: 'RESILIEE'
				};
			case 'CANCELLED':
				if (
					stateDetails?.fromState &&
					!['RESERVATION_NOTIFIED', 'RESERVATION_ACKNOWLEDGED', 'ACTIVATION_NOTIFIED', 'ACTIVATION_ACKNOWLEDGED'].includes(
						stateDetails.fromState
					)
				) {
					if (order?.currentNetworks?.cablingSpool?.activation?.acknowledgedAt) {
						return {
							code: '894',
							name: 'DECAB_AR_OK'
						};
					}

					if (order?.currentNetworks?.cablingSpool?.activation?.notifiedAt) {
						return {
							code: '896',
							name: 'DECAB_CR_OK'
						};
					}

					if (technology === 'PON_LNP' || technology === 'PON_CAD') {
						return {
							code: '893',
							name: 'DECABLAGE_COMMANDE'
						};
					}

					if (
						!order?.currentNetworks?.cablingSpool?.activation?.acknowledgedAt &&
						!order?.currentNetworks?.cablingSpool?.activation?.notifiedAt
					) {
						return {
							code: '892',
							name: 'FIBRE_A_DECABLER'
						};
					}

					return {
						code: '805',
						name: 'CAB_AR_NOK'
					};
				}
				if (stateDetails?.fromState === 'ACTIVATION_NOTIFIED') {
					return {
						code: '807',
						name: 'CAB_CR_NOK'
					};
				}
				if ((technology === 'PON_CAD' || technology === 'PON_LNP') && stateDetails?.fromState) {
					if (stateDetails.fromState === 'POSITIONED') {
						return technology === 'PON_CAD'
							? {
									code: '853',
									name: 'CABLAGE_PON_KO'
								}
							: {
									code: '855',
									name: 'CABLAGE_LNP_KO'
								};
					}
					if (
						![
							'POSITIONED',
							'RESERVATION_NOTIFIED',
							'RESERVATION_ACKNOWLEDGED',
							'ACTIVATION_NOTIFIED',
							'ACTIVATION_ACKNOWLEDGED'
						].includes(stateDetails.fromState)
					) {
						return technology === 'PON_CAD'
							? {
									code: '857',
									name: 'DECABLAGE_PON_KO'
								}
							: {
									code: '859',
									name: 'DECABLAGE_LNP_KO'
								};
					}
				}

				if (currentAppointment?.fromState === 'AWAITING') {
					return {
						code: '808',
						name: 'ERREUR_CABLAGE'
					};
				}

				return {
					code: '850',
					name: 'RESEAU_KO'
				};
			case 'ACTIVATION_NOTIFIED':
				return {
					code: '806',
					name: 'CAB_CR_OK'
				};
			case 'ACTIVATION_ACKNOWLEDGED':
				return {
					code: '810',
					name: 'EN_SERVICE'
				};
			default:
				return { code: '', name: '' };
		}
	};

	return `${getCode().code} ${getCode().name}`;
};

export const useNetworkStateCodeName = (network?: IFiberNetwork) => {
	const { order } = useOrder();

	const { data: networkHistory } = useQuery({
		...queries.fiber_network.byId(network?.['@id'])._ctx.workflowDetails,
		enabled: Boolean(order?.['@id'] && network?.['@id'])
	});

	const { data: currentAppointment } = useQuery({
		...queries.fiber_order.byId(order?.['@id'])._ctx.workflowHistory('appointments'),
		enabled: Boolean(order?.['@id'] && network?.['@id'])
	});

	const codeName =
		network?.state &&
		networkHistory &&
		order &&
		currentAppointment &&
		getNetworkCodeName(network.state, networkHistory, order, currentAppointment);

	return codeName;
};
