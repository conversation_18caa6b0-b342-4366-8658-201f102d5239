// utils
import { Typography } from '@tools/reactore';
// hooks
import { useOrder } from '@/hooks/queries/use-order';
// sections
import LogisticItemRow from './logistic-item-row';
// components
import Skeleton from '@/components-old/reactore/tou-migrate/skeleton';
import NotAvailable from '@/components-old/ui/not-available';

// ----------------------------------------------------------------------

function Logistic() {
	const { order } = useOrder();

	const logisticOrders = order?.logisticOrderItems
		?.map((logisticOrderItem) => logisticOrderItem.logisticOrder)
		?.filter((logisticOrder, index, arr) => arr.findIndex((item) => item?.['@id'] === logisticOrder?.['@id']) === index);

	if (order) {
		return (
			<section>
				<Typography type='h5' fontFamily='iliad' className='mb-2 mr-6 text-xl font-normal text-gray-400 dark:text-white'>
					Logistique
				</Typography>
				<div className='p-8 card rounded-2xl'>
					{order?.logisticOrderItems?.length ? (
						<>
							<table className='table-base'>
								<thead>
									<tr className='child:color-gray child:caption child:font-light'>
										<th>Tracking</th>
										<th>Statut</th>
										<th>Transporteur</th>
										{/* <th>Objet</th> */}
										<th>MAC / SN</th>
										<th>État</th>
									</tr>
								</thead>
								<tbody className='body-sm child:color-normal'>
									{Boolean(logisticOrders?.length) &&
										logisticOrders?.map(
											(logisticOrder) => logisticOrder && <LogisticItemRow logisticOrder={logisticOrder} />
										)}
								</tbody>
							</table>
							{/* <Button center variant='outlined' className='mt-12 text-gray-500'>
					<ExchangeIcon className='h-3 mr-3' />
					Échange et renvoi de matériel {WORKSITE}
				</Button> */}
						</>
					) : (
						<NotAvailable />
					)}
				</div>
			</section>
		);
	}

	return (
		<section>
			<Typography type='h5' fontFamily='iliad' className='mb-2 mr-6 text-xl font-normal text-gray-400 dark:text-white'>
				Logistique
			</Typography>
			<Skeleton className='w-full h-96 rounded-2xl' />
		</section>
	);
}
export default Logistic;
