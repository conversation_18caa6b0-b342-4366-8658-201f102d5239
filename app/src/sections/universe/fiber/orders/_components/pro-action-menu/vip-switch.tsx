import { Label } from '@/components/ui/label.tsx';
import { Switch } from '@/components/ui/switch.tsx';
import { useState } from 'react';
import { useEditOrder } from '@/api/mutations/fiber/order.mutation.ts';
import { useOrder } from '@/hooks/queries/use-order.ts';
import { toast } from '@/hooks/use-toast';
import { errorsGeneric } from '@/api/fetcher';

export default function VipSwitch() {
	const { order } = useOrder();

	const [isVIP, setIsVIP] = useState(order?.extraTracking);

	const editOrder = useEditOrder({ orderId: order?.reference as string });

	return (
		<div className='flex items-center space-x-2'>
			<Label htmlFor='vip-mode'>VIP</Label>

			<Switch
				id='vip-mode'
				className='data-[state=checked]:bg-gradient-to-r from-[#C41ED2]  to-[#CC0000]'
				defaultChecked={isVIP}
				onCheckedChange={async (set) => {
					setIsVIP(set);
					editOrder.mutate(
						{ extraTracking: set },
						{
							onSuccess: () => {
								toast({
									variant: 'success',
									title: 'Le mode VIP a bien été modifié'
								});
							},
							onError: errorsGeneric
						}
					);
				}}
			/>
		</div>
	);
}
