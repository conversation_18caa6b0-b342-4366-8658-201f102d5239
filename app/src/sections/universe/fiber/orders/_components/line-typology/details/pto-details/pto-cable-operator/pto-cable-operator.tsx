// utils
import { twJoin } from 'tailwind-merge';
// api
import type { IFiberPtoOutputs } from '@/api/interface/fiber';
// sections
import PtoEllipse from '../pto-ellipse';

// ----------------------------------------------------------------------

type Props = {
	id: string;
	color: string;
	ptoComponent: IFiberPtoOutputs;
};

export default function PtoCableOperator({ id, color, ptoComponent }: Props) {
	const getTwColor = (color: string) => {
		switch (color) {
			case 'red':
				return 'border-free-red-100 bg-free-red-50';
			case 'blue':
				return 'border-free-blue-200 bg-free-blue-50';
			case 'green':
				return 'border-green-200 bg-green-50';
			case 'yellow':
				return 'border-yellow-200 bg-yellow-50';
			default:
				break;
		}
	};

	return (
		<div className='mb-6 flex w-48 items-center rounded-full border border-free-gray-50 bg-white p-1'>
			<span className={twJoin('mr-2 flex h-8 w-8 items-center justify-center rounded-full border', getTwColor(color))}>{id}</span>
			<p className='flex-1'>{ptoComponent.operateur}</p>
			<div className='flex'>
				<PtoEllipse color={ptoComponent.colors.tube} />
				<PtoEllipse color={ptoComponent.colors.fiber} />
			</div>
		</div>
	);
}
