import { forwardRef, type ForwardRefExoticComponent, type RefAttributes, useState } from 'react';
// tanstack
import { useMutation } from '@tanstack/react-query';
// utils
import { getGenericEventState } from '@/utils/common.utils';
import { pluralize } from '@/utils/string.utils';
import { Wifi } from '@tools/reactor-icons';
import { Button, Typography } from '@tools/reactore';
import { snakeCase } from 'lodash';
// api
import type { IFiberPingOnu } from '@/api/interface/fiber';
import { fiberFetcher } from '@/api/fetcher';
// hooks
import { useOrder } from '@/hooks/queries/use-order';
import { toast } from '@/hooks/use-toast';
// sections
import CablesButton from './cables-button';
import TypologyCables from './typology-cables';
import TypologyDetails from './typology-details';
import TypologyItem from './typology-item';
import { BoxDetails, CollecteDetails, NroDetails, OnuDetails, PmDetails, PtoDetails } from './details';
// components
import Skeleton from '@/components-old/reactore/tou-migrate/skeleton';

// ----------------------------------------------------------------------

const LineTypology: ForwardRefExoticComponent<RefAttributes<HTMLElement>> = forwardRef((_, ref) => {
	const { order, vlan, rop, network, ip, oi, appointment, offerOrder, linkState, onu } = useOrder();

	const getPingOnuMutation = useMutation({
		mutationFn: () =>
			fiberFetcher<IFiberPingOnu>({
				url: `metrics/live/onu?oltIp=${ip?.privateIpv4}`
			}),
		onSuccess(rep) {
			switch (rep.status.toUpperCase()) {
				case 'ACTIF':
					toast({
						variant: 'success',
						// title: ,
						description: rep.status
					});
					break;
				case 'KO':
					toast({
						variant: 'error',
						// title: ,
						description: rep.status
					});
					break;
				default:
					toast({
						variant: 'warning',
						// title: '',
						description: 'Ip inconnu'
					});
			}
		},
		onError(err) {
			// @ts-ignore: to reviewn
			const { violations, 'hydra:description': hydraDescription } = err?.all?.response?.data || {};

			const errorTitle = 'Erreur serveur inconnue';
			const errorText = 'Une erreur est survenue';

			if (violations.length) {
				violations.forEach((violationItem: { message: any }) => {
					toast({
						variant: 'error',
						title: errorTitle,
						description: violationItem.message || errorText
					});
				});
			} else {
				toast({
					variant: 'error',
					title: errorTitle,
					description: hydraDescription || errorText
				});
			}
		}
	});

	const [tab, setTab] = useState<string | null>(null);

	const handleClick = (id: string) => {
		if (id === tab) {
			setTab(null);
			return;
		}
		setTab(id);
	};

	const pmToNroItemNetworks = rop?.networkComponents?.filter((item) => ['CAD', 'CTR', 'CDI'].includes(item.type));
	const ptoToPmItemNetworks = rop?.networkComponents?.filter((item) => ['PBO', 'FIBRE_CLIENT', 'FIBRE_PM'].includes(item.type));

	if (order) {
		return (
			<section id='typology' ref={ref}>
				<section className='flex items-center'>
					<div className='flex items-center'>
						<Typography type='h5' fontFamily='iliad' className='mr-6 text-xl font-normal text-gray-400 dark:text-white'>
							Typologie de la ligne
						</Typography>
						{oi?.technology && <div className='badge-gray'>{oi?.technology}</div>}
					</div>
					<div className='flex justify-end flex-1 gap-x-2'>
						{/* <Button className='flex items-center px-3 py-2 text-sm font-semibold bg-transparent border h-9 rounded-3xl border-free-gray-800 text-free-gray-800'> */}
						{/*	<CornerRightUp className='h-5 mr-2' /> Ping Box */}
						{/* </Button> */}

						{ip?.privateIpv4 && (
							<Button
								isLoading={getPingOnuMutation.isPending}
								onClick={() => getPingOnuMutation.mutate()}
								className='flex items-center px-3 py-2 text-sm font-semibold bg-transparent border h-9 rounded-3xl border-free-gray-800 text-free-gray-800'>
								<Wifi variant='solid' className='h-5 mr-2' /> Ping Onu
							</Button>
						)}

						{/* <Button className='flex items-center px-3 py-2 text-sm font-semibold bg-transparent border h-9 rounded-3xl border-free-gray-800 text-free-gray-800'> */}
						{/*	<Recording02 className='h-5 mr-2' /> Ping Télémétrie */}
						{/* </Button> */}
					</div>
				</section>
				<section className='flex mt-4 overflow-x-auto child:mr-2 child:last:mr-0'>
					{vlan?.['@type'] && (
						<TypologyItem
							id='collecte'
							description='Collecte'
							tab={tab}
							state={getGenericEventState(pluralize(vlan['@type']), vlan.state)}
							value={vlan.provider?.toUpperCase()}
							handleClick={handleClick}
						/>
					)}
					{network?.['@type'] && network.state && (
						<TypologyItem
							id='nro'
							description='NRO'
							tab={tab}
							state={network && getGenericEventState(pluralize(network['@type']), network.state)}
							value={rop?.nro?.name}
							handleClick={handleClick}
						/>
					)}
					{Boolean(pmToNroItemNetworks?.length) && (
						<CablesButton
							id='cablesPmToNro'
							tab={tab}
							value={pmToNroItemNetworks?.length ? pmToNroItemNetworks.length.toString() : '0'}
							handleClick={handleClick}
						/>
					)}
					{oi && oi['@type'] && oi.state && (
						<TypologyItem
							id='pm'
							description='PM'
							tab={tab}
							state={getGenericEventState(pluralize(oi['@type']), oi.state)}
							value={rop?.pm?.name}
							handleClick={handleClick}
						/>
					)}
					{Boolean(ptoToPmItemNetworks?.length) && (
						<CablesButton
							id='cablesPtoToPm'
							tab={tab}
							value={ptoToPmItemNetworks?.length ? ptoToPmItemNetworks.length.toString() : '0'}
							handleClick={handleClick}
						/>
					)}
					{appointment && appointment['@type'] && appointment.state && (
						<TypologyItem
							id='pto'
							description='PTO'
							tab={tab}
							state={getGenericEventState(pluralize(appointment?.['@type']), appointment?.state)}
							value={rop?.pto?.name}
							handleClick={handleClick}
						/>
					)}
					{linkState && linkState['@type'] && linkState.state && (
						<TypologyItem
							id='onu'
							tab={tab}
							description='ONU'
							state={getGenericEventState(pluralize(snakeCase(linkState['@type'])), linkState.state)}
							value={onu?.logisticOrderItem?.serial1}
							handleClick={handleClick}
						/>
					)}
					{linkState &&
						linkState['@type'] &&
						linkState.state &&
						order?.offerOrders.find((offerOrder) => offerOrder.offer.reference === 'box1') && (
							<TypologyItem
								id='box'
								tab={tab}
								description='Box'
								state={getGenericEventState(pluralize(snakeCase(linkState['@type'])), linkState.state)}
								value={offerOrder?.offer.name}
								handleClick={(id) => {
									if (offerOrder?.offer['@id'] !== '/offers/2') handleClick(id);
								}}
							/>
						)}
				</section>

				<TypologyDetails id='collecte' tab={tab}>
					<CollecteDetails />
				</TypologyDetails>
				<TypologyDetails id='nro' tab={tab}>
					<NroDetails />
				</TypologyDetails>
				<TypologyDetails id='pm' tab={tab}>
					<PmDetails />
				</TypologyDetails>
				<TypologyDetails id='pto' tab={tab}>
					<PtoDetails />
				</TypologyDetails>
				<TypologyDetails id='onu' tab={tab}>
					<OnuDetails />
				</TypologyDetails>
				<TypologyDetails className='p-0 overflow-hidden' id='box' tab={tab}>
					<BoxDetails />
				</TypologyDetails>
				<TypologyDetails id='cablesPtoToPm' tab={tab}>
					<TypologyCables itemNetworks={ptoToPmItemNetworks} />
				</TypologyDetails>
				<TypologyDetails id='cablesPmToNro' tab={tab}>
					<TypologyCables itemNetworks={pmToNroItemNetworks} />
				</TypologyDetails>
			</section>
		);
	}

	return (
		<section ref={ref}>
			<div className='flex items-end'>
				<Typography color='gray' type='h5' fontFamily='iliad' className='mr-6 text-xl font-normal'>
					Typologie de la ligne
				</Typography>
				<Skeleton className='w-24 h-6' />
			</div>
			<div className='flex items-end mt-4 space-x-2 overflow-x-auto child:mr-2 child:last:mr-0'>
				<div className='flex flex-col items-center space-y-1'>
					<Skeleton className='w-12 h-4' />
					<Skeleton className='h-12 rounded-lg w-36' />
				</div>
				<div className='flex flex-col items-center space-y-1'>
					<Skeleton className='w-12 h-4' />
					<Skeleton className='h-12 rounded-lg w-36' />
				</div>
				<Skeleton className='w-10 h-12 rounded-full' />
				<div className='flex flex-col items-center flex-1 space-y-1'>
					<Skeleton className='w-12 h-4' />
					<Skeleton className='w-full h-12 rounded-lg' />
				</div>
				<div className='flex flex-col items-center space-y-1'>
					<Skeleton className='w-12 h-4' />
					<Skeleton className='h-12 rounded-lg w-36' />
				</div>
				<Skeleton className='w-10 h-12 rounded-full' />
				<div className='flex flex-col items-center space-y-1'>
					<Skeleton className='w-12 h-4' />
					<Skeleton className='h-12 rounded-lg w-36' />
				</div>
				<div className='flex flex-col items-center space-y-1'>
					<Skeleton className='w-12 h-4' />
					<Skeleton className='h-12 rounded-lg w-36' />
				</div>
			</div>
		</section>
	);
});
export default LineTypology;
