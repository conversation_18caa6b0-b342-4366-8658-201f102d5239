import type { FC } from 'react';
// utils
import { twJoin } from 'tailwind-merge';

// ----------------------------------------------------------------------

type TPtoEllipse = {
	color: string;
};

const PtoEllipse: FC<TPtoEllipse> = ({ color }) => {
	return (
		<div
			className={twJoin(
				'bg-gree mx-1 h-3 w-3 rounded-full',
				color === 'red' && 'bg-red-300',
				color === 'purple' && 'bg-purple-300',
				color === 'green' && 'bg-green-300',
				color === 'yellow' && 'bg-yellow-400',
				color === 'blue' && 'bg-free-indigo-200',
				color === 'orange' && 'bg-free-orange-200',
				color === 'turquoise' && 'bg-teal-400'
			)}
		/>
	);
};
export default PtoEllipse;
