// utils
import { formatDate, Pagination, type HydraResponse, Typography } from '@tools/reactore';
import { twJoin } from 'tailwind-merge';
// api
import type { IFiberIncident } from '@/api/interface/fiber';
// components
import Tag from '@/components-old/reactore/tou-migrate/tag';
import NotAvailable from '@/components-old/ui/not-available';

// ----------------------------------------------------------------------

type Props = {
	incidents: HydraResponse<IFiberIncident>;
	page: number;
	setPage: any;
	itemsPerPage: number;
};
export default function IncidentsTabPanel({ incidents, page, setPage, itemsPerPage }: Props) {
	return (
		<>
			<section className='max-h-112.5 w-full'>
				<div className='mb-4 flex gap-x-2'>
					<Typography type='caption' color='gray' className='basis-28'>
						Statut
					</Typography>
					<Typography type='caption' color='gray' className='basis-44'>
						Début
					</Typography>
					<Typography type='caption' color='gray' className='basis-44'>
						Fin (estimé)
					</Typography>
					<Typography type='caption' color='gray' className='basis-32'>
						Impacté
					</Typography>
					<Typography type='caption' color='gray' className='flex-1'>
						Commentaire
					</Typography>
				</div>
				{incidents &&
					incidents['hydra:member'].map((incident) => (
						<div key={incident['@id']} className='mb-2 flex gap-x-2'>
							<div className='basis-28'>
								<Tag className={twJoin('w-fit', incident.deletedAt ? 'badge-success' : 'badge-warning')}>
									{incident.deletedAt ? 'Clôturé' : 'En attente'}
								</Tag>
							</div>
							<div className='basis-44'>{incident.startedAt && formatDate(incident.startedAt, 'dd/MM/yyyy hh:mm')}</div>
							<div className='basis-44'>{incident.endedAt && formatDate(incident.endedAt, 'dd/MM/yyyy hh:mm')}</div>
							<div className='basis-32'>{incident.origin}</div>
							<div className='max-h-24 flex-1 overflow-auto'>
								{incident.comment ? (
									incident.comment.split('\n').map((item, key) => (
										<span key={key}>
											{item}
											<br />
										</span>
									))
								) : (
									<NotAvailable />
								)}
							</div>
						</div>
					))}
			</section>
			<section className='flex h-10 w-full justify-end'>
				{incidents && <Pagination data={incidents} activePage={page} setActivePage={setPage} itemsPerPage={itemsPerPage} />}
				{/* <InputSelect */}
				{/*	className='md:h-7 md:w-[75px]' */}
				{/*	register={register} */}
				{/*	name='numberSelect' */}
				{/*	optionList={['20', '40', '50', '70', '100']} */}
				{/* /> */}
			</section>
		</>
	);
}
