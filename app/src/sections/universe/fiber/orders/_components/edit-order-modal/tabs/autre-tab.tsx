import { SwitchCard } from '@/components/ui/switch-card';

// ----------------------------------------------------------------------

export default function AutreTab() {
	return (
		<div className='space-y-6'>
			<div className='grid grid-cols-2 gap-4'>
				<div className='space-y-4'>
					<SwitchCard
						name='commandeTest'
						label='Commande de test'
						activeLabel='Actif'
						inactiveLabel='Inactif'
						badgeVariant='green'
					/>

					<SwitchCard
						name='modeAutomatique'
						label='Mode automatique'
						activeLabel='Actif'
						inactiveLabel='Inactif'
						badgeVariant='green'
					/>
				</div>

				<div className='space-y-4'>
					<SwitchCard name='vip' label='VIP' activeLabel='Actif' inactiveLabel='Inactif' badgeVariant='green' />
				</div>
			</div>
		</div>
	);
}
