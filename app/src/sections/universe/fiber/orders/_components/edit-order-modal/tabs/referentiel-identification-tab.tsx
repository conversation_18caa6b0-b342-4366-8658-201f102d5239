// utils
import { useFormContext } from 'react-hook-form';
import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
// components
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { FormLabelRequired } from '@/components/ui/form-label-required';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
	Combobox,
	ComboboxInput,
	ComboboxTrigger,
	ComboboxContent,
	ComboboxEmpty,
	ComboboxGroup,
	ComboboxItem
} from '@/components/ui/combobox';
// api
import { fiberCompanyQueryKeys } from '@/api/queries/fiber/company.query';
// types
import type { EditOrderFormData } from '../schema';
import type { ICellCompany } from '@/api/interface/cell';

// ----------------------------------------------------------------------

const RETAILER_OPTIONS = [
	{ value: 'XPR', label: 'XPR' },
	{ value: 'FREEPRO', label: 'Free Pro' },
	{ value: 'WHOLESALE', label: 'Wholesale' },
	{ value: 'FTTO', label: 'FTTO' }
] as const;

function useDebounce<T>(value: T, delay: number): T {
	const [debouncedValue, setDebouncedValue] = useState<T>(value);

	useEffect(() => {
		const handler = setTimeout(() => {
			setDebouncedValue(value);
		}, delay);

		return () => {
			clearTimeout(handler);
		};
	}, [value, delay]);

	return debouncedValue;
}

export default function ReferentielIdentificationTab() {
	const { control } = useFormContext<EditOrderFormData>();

	return (
		<div className='space-y-6'>
			<div className='grid grid-cols-2 gap-4'>
				{/* Column 1 */}
				<div className='space-y-4'>
					<FormField
						control={control}
						name='jetonCommande'
						render={({ field }) => (
							<FormItem>
								<FormLabelRequired required>Jeton de commande</FormLabelRequired>
								<FormControl>
									<Input placeholder='Jeton de commande' className={'bg-card'} {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField control={control} name='entrepriseCliente' render={({ field }) => <CompanyAutocomplete field={field} />} />

					<FormField
						control={control}
						name='retailer'
						render={({ field }) => (
							<FormItem>
								<FormLabelRequired required>Retailer</FormLabelRequired>
								<Select onValueChange={field.onChange} value={field.value}>
									<FormControl>
										<SelectTrigger className='bg-card'>
											<SelectValue placeholder='Sélectionner un retailer' />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										{RETAILER_OPTIONS.map((option) => (
											<SelectItem key={option.value} value={option.value}>
												{option.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				{/* Column 2 */}
				<div className='space-y-4'>
					<FormField
						control={control}
						name='referenceBDC'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Référence BDC</FormLabel>
								<FormControl>
									<Input placeholder='Référence BDC' {...field} className={'bg-card'} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name='referenceEP'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Référence EP</FormLabel>
								<FormControl>
									<Input placeholder='Référence EP' {...field} className={'bg-card'} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name='referenceJFP'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Référence de service</FormLabel>
								<FormControl>
									<Input placeholder='JFP0000000000' {...field} className={'bg-card'} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
			</div>

			{/* Full-width input */}
			<FormField
				control={control}
				name='entrepriseDistributeur'
				render={({ field }) => (
					<FormItem>
						<FormLabel>Si Wholesale : Entreprise distributeur</FormLabel>
						<FormControl>
							<Input placeholder='Entreprise distributeur' {...field} className={'bg-card'} />
						</FormControl>
						<FormMessage />
					</FormItem>
				)}
			/>
		</div>
	);
}

function CompanyAutocomplete({ field }: { field: any }) {
	const [searchTerm, setSearchTerm] = useState('');
	const [isOpen, setIsOpen] = useState(false);
	const debouncedSearchTerm = useDebounce(searchTerm, 300);

	const { data: companiesResponse, isLoading } = useQuery({
		...fiberCompanyQueryKeys.byName(debouncedSearchTerm),
		enabled: debouncedSearchTerm.length >= 2,
		staleTime: 5 * 60 * 1000 // 5 minutes
	});

	const { data: all } = useQuery({
		...fiberCompanyQueryKeys.all({
			page: 1
		}),
		enabled: true
	});

	console.log('all', all);

	const companies = companiesResponse?.['hydra:member'] || [];

	console.log('companies', companies);

	const handleSelect = (company: ICellCompany) => {
		field.onChange(company.name);
		setSearchTerm(company.name || '');
		setIsOpen(false);
	};

	const handleInputChange = (value: string) => {
		setSearchTerm(value);
		field.onChange(value);
		setIsOpen(value.length >= 2);
	};

	return (
		<FormItem>
			<FormLabelRequired required>Entreprise cliente</FormLabelRequired>
			<Combobox open={isOpen} onOpenChange={setIsOpen}>
				<FormControl>
					<div className='relative'>
						<ComboboxInput
							placeholder='Rechercher une entreprise...'
							value={searchTerm}
							onChange={(e) => handleInputChange(e.target.value)}
							className='bg-card border border-input rounded-md px-3 py-2 w-full'
						/>
						<ComboboxTrigger className='absolute right-2 top-1/2 -translate-y-1/2' />
					</div>
				</FormControl>
				<ComboboxContent>
					{isLoading ? (
						<div className='p-2 text-sm text-muted-foreground'>Recherche en cours...</div>
					) : companies.length > 0 ? (
						<ComboboxGroup>
							{companies.map((company) => (
								<ComboboxItem
									key={company['@id']}
									value={company.name || ''}
									onSelect={() => handleSelect(company)}
									className='cursor-pointer'>
									<div className='flex flex-col'>
										<span className='font-medium'>{company.name}</span>
										{company.legalName && company.legalName !== company.name && (
											<span className='text-sm text-muted-foreground'>{company.legalName}</span>
										)}
									</div>
								</ComboboxItem>
							))}
						</ComboboxGroup>
					) : debouncedSearchTerm.length >= 2 ? (
						<ComboboxEmpty>Aucune entreprise trouvée</ComboboxEmpty>
					) : (
						<div className='p-2 text-sm text-muted-foreground'>Tapez au moins 2 caractères pour rechercher</div>
					)}
				</ComboboxContent>
			</Combobox>
			<FormMessage />
		</FormItem>
	);
}
