// utils
import { useFormContext } from 'react-hook-form';
import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
// components
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { FormLabelRequired } from '@/components/ui/form-label-required';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
// api
import { fiberCompanyQueryKeys } from '@/api/queries/fiber/company.query';
// types
import type { EditOrderFormData } from '../schema';
import type { ICellCompany } from '@/api/interface/cell';

// ----------------------------------------------------------------------

const RETAILER_OPTIONS = [
	{ value: 'XPR', label: 'XPR' },
	{ value: 'FREEPRO', label: 'Free Pro' },
	{ value: 'WHOLESALE', label: 'Wholesale' },
	{ value: 'FTTO', label: 'FTTO' }
] as const;

function useDebounce<T>(value: T, delay: number): T {
	const [debouncedValue, setDebouncedValue] = useState<T>(value);

	useEffect(() => {
		const handler = setTimeout(() => {
			setDebouncedValue(value);
		}, delay);

		return () => {
			clearTimeout(handler);
		};
	}, [value, delay]);

	return debouncedValue;
}

export default function ReferentielIdentificationTab() {
	const { control } = useFormContext<EditOrderFormData>();

	return (
		<div className='space-y-6'>
			<div className='grid grid-cols-2 gap-4'>
				{/* Column 1 */}
				<div className='space-y-4'>
					<FormField
						control={control}
						name='jetonCommande'
						render={({ field }) => (
							<FormItem>
								<FormLabelRequired required>Jeton de commande</FormLabelRequired>
								<FormControl>
									<Input placeholder='Jeton de commande' className={'bg-card'} {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField control={control} name='entrepriseCliente' render={({ field }) => <CompanyAutocomplete field={field} />} />

					<FormField
						control={control}
						name='retailer'
						render={({ field }) => (
							<FormItem>
								<FormLabelRequired required>Retailer</FormLabelRequired>
								<Select onValueChange={field.onChange} value={field.value}>
									<FormControl>
										<SelectTrigger className='bg-card'>
											<SelectValue placeholder='Sélectionner un retailer' />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										{RETAILER_OPTIONS.map((option) => (
											<SelectItem key={option.value} value={option.value}>
												{option.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				{/* Column 2 */}
				<div className='space-y-4'>
					<FormField
						control={control}
						name='referenceBDC'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Référence BDC</FormLabel>
								<FormControl>
									<Input placeholder='Référence BDC' {...field} className={'bg-card'} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name='referenceEP'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Référence EP</FormLabel>
								<FormControl>
									<Input placeholder='Référence EP' {...field} className={'bg-card'} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name='referenceJFP'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Référence de service</FormLabel>
								<FormControl>
									<Input placeholder='JFP0000000000' {...field} className={'bg-card'} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
			</div>

			{/* Full-width input */}
			<FormField
				control={control}
				name='entrepriseDistributeur'
				render={({ field }) => (
					<FormItem>
						<FormLabel>Si Wholesale : Entreprise distributeur</FormLabel>
						<FormControl>
							<Input placeholder='Entreprise distributeur' {...field} className={'bg-card'} />
						</FormControl>
						<FormMessage />
					</FormItem>
				)}
			/>
		</div>
	);
}

function CompanyAutocomplete({ field }: { field: any }) {
	const [open, setOpen] = useState(false);
	const [searchTerm, setSearchTerm] = useState('');
	const debouncedSearchTerm = useDebounce(searchTerm, 300);

	// Query companies when search term changes
	const { data: companiesResponse, isLoading } = useQuery({
		...fiberCompanyQueryKeys.byName(debouncedSearchTerm),
		enabled: debouncedSearchTerm.length >= 2,
		staleTime: 5 * 60 * 1000 // 5 minutes
	});

	const companies = companiesResponse?.['hydra:member'] || [];

	const handleSelect = (company: ICellCompany) => {
		field.onChange(company.name);
		setOpen(false);
	};

	const displayValue = field.value || '';

	return (
		<FormItem>
			<FormLabelRequired required>Entreprise cliente</FormLabelRequired>
			<Popover open={open} onOpenChange={setOpen}>
				<PopoverTrigger asChild>
					<FormControl>
						<Button
							variant='outline'
							role='combobox'
							aria-expanded={open}
							className={cn(
								'w-full justify-between bg-card border-input text-left font-normal',
								'hover:bg-card focus:ring-0 focus:ring-offset-0',
								!displayValue && 'text-muted-foreground'
							)}>
							{displayValue || 'Rechercher une entreprise...'}
							<ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
						</Button>
					</FormControl>
				</PopoverTrigger>
				<PopoverContent className='w-[--radix-popover-trigger-width] max-w-none p-0' align='start'>
					<Command className='rounded-lg border shadow-md'>
						<div className='flex items-center border-b px-3 w-full'>
							<CommandInput
								placeholder='Rechercher une entreprise...'
								value={searchTerm}
								onValueChange={setSearchTerm}
								className='flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50 border-0 focus:ring-0 focus:ring-offset-0'
							/>
						</div>
						<CommandList className='max-h-[200px]'>
							{isLoading ? (
								<div className='p-4 text-sm text-muted-foreground text-center'>Recherche en cours...</div>
							) : companies.length > 0 ? (
								<CommandGroup>
									{companies.map((company) => (
										<CommandItem
											key={company['@id']}
											value={company.name || ''}
											onSelect={() => handleSelect(company)}
											className='cursor-pointer'>
											<Check
												className={cn('mr-2 h-4 w-4', field.value === company.name ? 'opacity-100' : 'opacity-0')}
											/>
											<div className='flex flex-col'>
												<span className='font-medium'>{company.name}</span>
												{company.legalName && company.legalName !== company.name && (
													<span className='text-sm text-muted-foreground'>{company.legalName}</span>
												)}
											</div>
										</CommandItem>
									))}
								</CommandGroup>
							) : debouncedSearchTerm.length >= 2 ? (
								<CommandEmpty>Aucune entreprise trouvée</CommandEmpty>
							) : (
								<div className='p-4 text-sm text-muted-foreground text-center'>
									Tapez au moins 2 caractères pour rechercher
								</div>
							)}
						</CommandList>
					</Command>
				</PopoverContent>
			</Popover>
			<FormMessage />
		</FormItem>
	);
}
