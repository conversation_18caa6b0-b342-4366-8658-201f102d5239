// utils
import { useFormContext } from 'react-hook-form';
// components
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { FormLabelRequired } from '@/components/ui/form-label-required';
import { Input } from '@/components/ui/input';
// types
import type { EditOrderFormData } from '../schema';

// ----------------------------------------------------------------------

export default function ReferentielGeographiqueTab() {
	const { control } = useFormContext<EditOrderFormData>();

	return (
		<div className='space-y-6'>
			<div className='grid grid-cols-2 gap-4'>
				{/* Column 1 */}
				<div className='space-y-4'>
					<FormField
						control={control}
						name='iwAdresseId'
						render={({ field }) => (
							<FormItem>
								<FormLabelRequired required>IW Adresse ID</FormLabelRequired>
								<FormControl>
									<Input placeholder='IW Adresse ID' {...field} className={'bg-card'} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name='latitude'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Latitude (x)</FormLabel>
								<FormControl>
									<Input placeholder='Latitude' {...field} className={'bg-card'} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name='longitude'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Longitude (y)</FormLabel>
								<FormControl>
									<Input placeholder='Longitude' {...field} className={'bg-card'} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name='complementAdresse'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Complément d'adresse</FormLabel>
								<FormControl>
									<Input placeholder="Complément d'adresse" {...field} className={'bg-card'} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				{/* Column 2 */}
				<div className='space-y-4'>
					<FormField
						control={control}
						name='batimentIMB'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Batiment IMB</FormLabel>
								<FormControl>
									<Input placeholder='Batiment IMB' {...field} className={'bg-card'} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name='referenceAPCBatiment'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Référence APC - Bâtiment</FormLabel>
								<FormControl>
									<Input placeholder='Référence APC - Bâtiment' {...field} className={'bg-card'} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name='referenceAPCEscalier'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Référence APC - Escalier</FormLabel>
								<FormControl>
									<Input placeholder='Référence APC - Escalier' {...field} className={'bg-card'} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name='referenceAPCEtage'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Référence APC - Étage</FormLabel>
								<FormControl>
									<Input placeholder='Référence APC - Étage' {...field} className={'bg-card'} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
			</div>
		</div>
	);
}
