import {
	Copy05,
	GridDotsBlank,
	GridDotsHorizontalCenter,
	GridDotsOuter,
	GridDotsVerticalCenter,
	Map01,
	StopSquare,
	ZoomIn
} from '@tools/reactor-icons';
import { Badge } from '@/components/ui/badge.tsx';
import { useOrder } from '@/hooks/queries/use-order.ts';
import { toast } from '@/hooks/use-toast.ts';

export default function AddressInfo() {
	const { order, oi, adel } = useOrder();

	return (
		<div className='w-full'>
			<div className='flex items-center justify-between mb-4 pr-2'>
				<div className='flex items-center gap-2'>
					<svg width='27' height='38' viewBox='0 0 27 38' fill='none' xmlns='http://www.w3.org/2000/svg'>
						<g clip-path='url(#clip0_6897_133324)'>
							<path
								d='M13.4497 38C13.4497 38 3.44607 25.7903 1 17.5C0.397343 15.4574 0.500017 14 0.500017 14L26.5 14C26.5 14 26.5 16 26.0002 17.5C23.0331 26.4057 13.4497 38 13.4497 38Z'
								fill='#7E22CE'
							/>
							<rect x='0.5' y='0.706055' width='26' height='26' rx='13' fill='#7E22CE' />
							<g clip-path='url(#clip1_6897_133324)'>
								<path
									d='M16.1663 14.6223C18.5209 15.0853 20.1663 16.1425 20.1663 17.3727C20.1663 19.0296 17.1816 20.3727 13.4997 20.3727C9.81778 20.3727 6.83301 19.0296 6.83301 17.3727C6.83301 16.1425 8.47847 15.0853 10.833 14.6223M13.4997 17.0394V7.70605L17.0448 9.88768C17.3034 10.0468 17.4327 10.1264 17.474 10.2266C17.5099 10.3141 17.5071 10.4127 17.4661 10.4979C17.4192 10.5956 17.2855 10.6675 17.0182 10.8115L13.4997 12.7061'
									stroke='white'
									stroke-width='1.5'
									stroke-linecap='round'
									stroke-linejoin='round'
								/>
							</g>
						</g>
						<defs>
							<clipPath id='clip0_6897_133324'>
								<rect width='27' height='38' fill='white' />
							</clipPath>
							<clipPath id='clip1_6897_133324'>
								<rect width='16' height='16' fill='white' transform='translate(5.5 5.70605)' />
							</clipPath>
						</defs>
					</svg>

					<div>
						<div className='flex items-center gap-2'>
							<h1 className='text-lg font-semibold'>{`${order?.endpointAddress?.street} ${order?.endpointAddress?.postalCode} ${order?.endpointAddress?.city}`}</h1>
							{adel?._source?.eligibilities?.ftth?.is ? (
								<Badge variant='green'>Éligible</Badge>
							) : (
								<Badge variant='red'>Non éligible</Badge>
							)}
						</div>
						<div className='flex items-center text-sm'>
							<span>{`${order?.endpointAddress?.longitude}, ${order?.endpointAddress?.latitude}`}</span>
							<Copy05
								size={16}
								className='ml-2 cursor-pointer'
								onClick={() => {
									navigator.clipboard.writeText(
										`${order?.endpointAddress?.longitude}, ${order?.endpointAddress?.latitude}`
									);
									toast({
										title: 'Coordonnées copiées',
										description: 'Les coordonnées ont été copiées dans le presse-papiers.',
										duration: 2000
									});
								}}
							/>
							<div className='bg-foreground text-background rounded-full px-1.5 py-0.5 ml-2 text-xs font-bold'>+12</div>
						</div>
					</div>
				</div>
				<div className='flex items-start h-10 gap-4'>
					<button className='flex items-center text-[#7e22ce] text-sm'>
						<ZoomIn className='h-4 w-4 mr-1' />
						<span>Commandes à l'adresse</span>
					</button>
					<button className='flex items-center text-[#7e22ce] text-sm'>
						<GridDotsBlank className='h-4 w-4 mr-1' />
						<span>Détails verticalité</span>
					</button>
					<button className='flex items-center text-[#7e22ce] text-sm'>
						<Map01 className='h-4 w-4 mr-1' />
						<span>Détails adresse</span>
					</button>
				</div>
			</div>

			<div className='grid grid-cols-12 gap-4'>
				<div className='col-span-6'>
					<div className='flex gap-2 items-baseline mb-2'>
						<div className=' text-[#161616] font-medium '>Référentiel adresse</div>
						<Badge variant='gray' className={'py-0 px-1.5'}>
							Actif
						</Badge>
					</div>
					<div className='grid grid-cols-4 gap-x-4 gap-y-6 border border-[#e5e5e5] rounded-lg p-2'>
						<div className={'flex gap-2'}>
							<img
								src={`/assets/images/logo/${order?.currentOis?.operatorName?.toLowerCase()}.png`}
								alt={`${order?.currentOis?.operatorName}.png`}
								className='h-10 rounded-sm'
							/>
							<div>
								<p className='text-xs text-[#919eab] mb-1'>Exploitant</p>
								<div className='flex items-center'>
									<span className='text-[#212b36]'>{order?.currentOis?.operatorName}</span>
								</div>
							</div>
						</div>
						<div>
							<p className='text-xs text-[#919eab] mb-1'>Infrastructure</p>
							<p className='text-[#212b36]'>{oi?.name}</p>
						</div>

						<div>
							<p className='text-xs text-[#919eab] mb-1'>Opérateur de gestion</p>
							<p className='text-[#212b36]'>TODO</p>
						</div>
						<div>
							<p className='text-xs text-[#919eab] mb-1'>Zone</p>
							<p className='text-[#212b36]'>{adel?._source.adel.providers?.[0].zonePm}</p>
						</div>

						<div>
							<p className='text-xs text-[#919eab] mb-1'>Hexaclé</p>
							<p className='text-[#212b36]'>{order?.endpointAddress?.hexacle}</p>
						</div>
						<div>
							<p className='text-xs text-[#919eab] mb-1'>IW adresse ID</p>
							<p className='text-[#7e22ce]'>{order?.endpointAddress?.iwId}</p>
						</div>

						<div>
							<p className='text-xs text-[#919eab] mb-1'>Clé inter-opérateur</p>
							<p className='text-[#212b36]'>{adel?._source.cleInterop}</p>
						</div>
						<div>
							<p className='text-xs text-[#919eab] mb-1'>Département</p>
							<p className='text-[#212b36]'>{adel?._source.adel.department}</p>
						</div>
					</div>
				</div>

				<div className='col-span-3'>
					<p className='text-sm text-[#6f6f6f] mb-2'>
						Verticalité <span className='font-medium text-[#161616]'>commandée</span>
					</p>
					<div className='border border-[#e5e5e5] rounded-lg p-2'>
						<div className='space-y-2'>
							<div className={'flex gap-2 items-center'}>
								<GridDotsOuter size={18} className={'text-gray-400'} />
								<p className='text-gray-400'>Bâtiment</p>
								<p>{order?.ois?.[0]?.buildingName}</p>
							</div>
							<div className={'flex gap-2 items-center'}>
								<GridDotsHorizontalCenter size={18} className={'text-gray-400'} />
								<p className='text-gray-400'>Escalier</p>
								<p>{order?.ois?.[0]?.floorName}</p>
							</div>
							<div className={'flex gap-2 items-center'}>
								<GridDotsVerticalCenter size={18} className={'text-gray-400'} />
								<p className='text-gray-400'>Étage</p>
								<p>{order?.ois?.[0]?.stairCaseName}</p>
							</div>
							<div className={'flex gap-2 items-center'}>
								<StopSquare size={18} className={'text-gray-400'} />
								<p className='text-gray-400'>PTO</p>
								<p>{order?.ois?.[0]?.ptoName}</p>
							</div>
						</div>
					</div>
				</div>

				<div className='col-span-3'>
					<div className={'w-full flex justify-between mb-1.5'}>
						<p className='text-sm text-[#6f6f6f]'>
							Verticalité <span className='font-medium text-[#161616]'>actuelle</span>
							{
								//adel?._source.beli[0].Sources
							}
						</p>
						<div className='flex items-center gap-1'>
							{order?.endpointBuilding?.imb}
							<Badge variant='green' size={'sm'}>
								Éligible
							</Badge>
						</div>
					</div>
					<div className='border border-[#e5e5e5] rounded-lg p-2'>
						<div className='space-y-2'>
							<div className={'flex gap-2 items-center'}>
								<GridDotsOuter size={18} className={'text-gray-400'} />
								<p className='text-gray-400'>Bâtiment</p>
								<p>{order?.currentOis?.buildingName}</p>
							</div>
							<div className={'flex gap-2 items-center'}>
								<GridDotsHorizontalCenter size={18} className={'text-gray-400'} />
								<p className='text-gray-400'>Escalier</p>
								<p>{order?.currentOis?.floorName}</p>
							</div>
							<div className={'flex gap-2 items-center'}>
								<GridDotsVerticalCenter size={18} className={'text-gray-400'} />
								<p className='text-gray-400'>Étage</p>
								<p>{order?.currentOis?.stairCaseName}</p>
							</div>
							<div className={'flex gap-2 items-center'}>
								<StopSquare size={18} className={'text-gray-400'} />
								<p className='text-gray-400'>PTO</p>
								<p>{order?.currentOis?.ptoName}</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
