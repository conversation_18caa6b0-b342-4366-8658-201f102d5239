import { forwardRef, type ForwardRefExoticComponent, type RefAttributes } from 'react';
// tanstack
import { useParams } from '@tanstack/react-router';
// utils
import { Tooltip, Typography } from '@tools/reactore';
// hooks
import { useOrder } from '@/hooks/queries/use-order';
// sections
import ReferenceFormater from '@/sections/_components/reference-formater';
import RetailerIcon from '@/sections/_components/retailer-icon';
// components
import Skeleton from '@/components-old/reactore/tou-migrate/skeleton';
import { RefreshIcon, ShoppingBag2Icon } from '@/components-old/ui/icon';
import { useToast } from '@/hooks/use-toast.ts';
import { Badge } from '@/components/ui/badge.tsx';
import StateBadge from '@/components/custom/state-badge';

// ----------------------------------------------------------------------

const FiberFtthOrdersHeader: ForwardRefExoticComponent<RefAttributes<HTMLElement>> = forwardRef<HTMLElement>((_, ref) => {
	const { id } = useParams({ strict: false });
	const { order, oi } = useOrder();

	const { toast: showToast } = useToast();
	const copyOrderId = () => {
		navigator.clipboard.writeText(id as string);

		const { dismiss } = showToast({
			variant: 'success',
			description: 'Jeton copié',
			duration: 1000
		});

		setTimeout(() => {
			dismiss();
		}, 1000);
	};

	if (order) {
		return (
			<section className='mb-2 flex justify-between w-full items-end' ref={ref}>
				<div className='flex items-center gap-3 items-baseline'>
					{typeof order?.retailer !== 'string' && order.retailer?.name && <RetailerIcon name={order.retailer.name} />}
					<div className='flex'>
						<div className='flex' onClick={copyOrderId}>
							<ReferenceFormater className='select-none' str={order?.reference || 'N/A'} />
						</div>
					</div>

					<div className='flex gap-2 items-baseline'>
						<div>{order?.era && <StateBadge era={order.era} />}</div>

						{order?.extraTracking && (
							<div className={'bg-[#F9E6E8] rounded-md px-2.5 py-0.5 text-xs font-semibold  flex gap-1 items-center'}>
								<svg width='14' height='12' viewBox='0 0 14 12' fill='none' xmlns='http://www.w3.org/2000/svg'>
									<path
										d='M2.1311 2.78166C2.67016 1.88322 3.64109 1.3335 4.68883 1.3335H9.31117C10.3589 1.3335 11.3298 1.88322 11.8689 2.78166V2.78166C12.5415 3.90265 12.4058 5.33097 11.5341 6.30522L7.33333 11.0002C7.28988 11.0445 7.23802 11.0797 7.18078 11.1038C7.12355 11.1278 7.06209 11.1402 7 11.1402C6.93792 11.1402 6.87645 11.1278 6.81922 11.1038C6.76198 11.0797 6.71012 11.0445 6.66667 11.0002L2.46593 6.30522C1.59424 5.33097 1.45851 3.90265 2.1311 2.78166V2.78166Z'
										stroke='url(#paint0_linear_7305_20644)'
										stroke-width='1.5'
										stroke-linecap='round'
										stroke-linejoin='round'
									/>
									<path
										d='M5.66634 6.00003L4.33301 4.53337L4.73301 3.8667'
										stroke='url(#paint1_linear_7305_20644)'
										stroke-width='1.33333'
										stroke-linecap='round'
										stroke-linejoin='round'
									/>
									<defs>
										<linearGradient
											id='paint0_linear_7305_20644'
											x1='13.0923'
											y1='10.7239'
											x2='0.907653'
											y2='10.7238'
											gradientUnits='userSpaceOnUse'>
											<stop stop-color='#CC0000' />
											<stop offset='1' stop-color='#C41ED2' />
										</linearGradient>
										<linearGradient
											id='paint1_linear_7305_20644'
											x1='5.6766'
											y1='5.90946'
											x2='4.32275'
											y2='5.90945'
											gradientUnits='userSpaceOnUse'>
											<stop stop-color='#CC0000' />
											<stop offset='1' stop-color='#C41ED2' />
										</linearGradient>
									</defs>
								</svg>
								<div className={'bg-gradient-to-r from-[#C41ED2]  to-[#CC0000] inline-block text-transparent bg-clip-text'}>
									VIP
								</div>
							</div>
						)}
						{order?.wholesaleReference && <Badge variant='gray'>Wholesale</Badge>}
					</div>
				</div>
				<div className='flex gap-3'>
					<div className='text-sm font-bold flex'>OI: {oi?.reference ? oi?.reference : 'N/A'}</div>
					<div className='text-sm font-bold flex'>BDC: {order?.customerReference ? order?.customerReference : 'N/A'}</div>
					{order?.ois?.[0]?.isMigration && (
						<Tooltip position='top' content='Migration BtoC vers BtoB'>
							<RefreshIcon />
						</Tooltip>
					)}
					{order.acquisition === 'shop' && (
						<Tooltip position='top' content='Commande passée en boutique Free'>
							<div className='rounded-full bg-white p-1.5'>
								<ShoppingBag2Icon className='text-free-red' />
							</div>
						</Tooltip>
					)}
				</div>
			</section>
		);
	}

	return (
		<section className='mb-4 flex justify-between'>
			<div className='flex items-center'>
				<Skeleton className='mr-4 h-10 w-10 rounded-full' />
				<div className='flex'>
					<div>
						<Typography className='text-xs font-semibold' color='gray'>
							Jeton de commande
						</Typography>
						<Skeleton className='h-9 w-36' />
					</div>
				</div>

				<div className='ml-11 flex child:mr-10'>
					<div>
						<Typography type='caption' color='gray'>
							Statut
						</Typography>
						<Skeleton className='w-21' />
					</div>
					<div>
						<Typography type='caption' color='gray'>
							Bon de commande
						</Typography>
						<Skeleton className='w-21' />
					</div>
					<Skeleton className='h-10 w-16' />
				</div>
			</div>
			<div className='flex self-end last:mr-0 child:mr-2'>
				<Skeleton className='h-9 w-9' />
			</div>
		</section>
	);
}) as ForwardRefExoticComponent<RefAttributes<HTMLElement>>;

export default FiberFtthOrdersHeader;
