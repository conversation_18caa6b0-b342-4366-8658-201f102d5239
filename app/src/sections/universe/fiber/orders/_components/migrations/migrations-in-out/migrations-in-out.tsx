// utils
import { getColorStateOrder } from '@/utils/common.utils';
import { formatDate, Typography } from '@tools/reactore';
import { twJoin } from 'tailwind-merge';
// hooks
import { useOrder } from '@/hooks/queries/use-order';
// components
import Skeleton from '@/components-old/reactore/tou-migrate/skeleton';
import Tag from '@/components-old/reactore/tou-migrate/tag';
import Icon from '@/components-old/ui/icon';
import NotAvailable from '@/components-old/ui/not-available';

// ----------------------------------------------------------------------

type Props = {
	type: 'in' | 'out';
};
export default function MigrationsInOut({ type }: Props) {
	const { order, oi, rop } = useOrder();

	const isMigrationOut = type === 'out';

	const migrationsInOut = oi?.migrations?.filter((migrationItem) =>
		isMigrationOut ? migrationItem.direction === 'outbound' : migrationItem.direction === 'inbound'
	);

	if (order) {
		return (
			<div className='w-full card rounded-2xl'>
				{migrationsInOut?.length ? (
					migrationsInOut.map((migrationInOutItem) => (
						<div className='w-full p-6 pb-3 mb-4'>
							<div className='flex items-start justify-between mb-5'>
								<div className='flex'>
									<div>
										<Typography type='caption' color='gray'>
											Type
										</Typography>
										<Typography
											className={twJoin(
												'flex items-center',
												isMigrationOut ? 'text-free-state-error' : 'text-free-state-info'
											)}>
											{isMigrationOut ? 'Sortante' : 'Entrante'}
											<Icon name={isMigrationOut ? 'arrow_up_right' : 'arrow_down_right'} className='h-3 ml-1' />
										</Typography>
									</div>
									{oi?.originalSourceType && (
										<Tag className='ml-4 badge-gray'>
											{oi?.originalSourceType} {order.source}
										</Tag>
									)}
								</div>
								<Tag
									className={twJoin(
										'rounded-full text-xs',
										migrationInOutItem?.endDate && getColorStateOrder('DONE'),
										!migrationInOutItem?.endDate &&
											getColorStateOrder(migrationInOutItem.isOk ? 'WAITING' : 'TERMINATED')
									)}>
									{migrationInOutItem?.endDate && 'Terminé'}
									{!migrationInOutItem?.endDate && (migrationInOutItem.isOk ? 'Actif' : 'KO')}
								</Tag>
								{/* <div title='biento disponible'> */}
								{/*	<MagnifyingGlassIcon className='h-5' /> */}
								{/* </div> */}
							</div>

							<div className='flex justify-between w-full'>
								<div>
									<Typography type='caption' color='gray'>
										B2C tech id
									</Typography>
									{order?.currentNetworks?.migrationIwTechId ? (
										<Typography>{order?.currentNetworks?.migrationIwTechId}</Typography>
									) : (
										<NotAvailable />
									)}
								</div>

								<div>
									<Typography type='caption' color='gray'>
										Date de début
									</Typography>
									{new Date(migrationInOutItem.endDate) > new Date(migrationInOutItem.startDate) ? (
										<Typography>
											{migrationInOutItem.startDate && formatDate(migrationInOutItem.startDate, 'dd/MM/yyyy')}
										</Typography>
									) : (
										<NotAvailable />
									)}
								</div>
								<div>
									<Typography type='caption' color='gray'>
										Date de fin
									</Typography>
									{new Date(migrationInOutItem.endDate) > new Date(migrationInOutItem.startDate) ? (
										<Typography>
											{migrationInOutItem.endDate && formatDate(migrationInOutItem.endDate, 'dd/MM/yyyy')}
										</Typography>
									) : (
										<NotAvailable />
									)}
								</div>

								{type === 'out' && (
									<div>
										<Typography type='caption' color='gray'>
											Reprise de position
										</Typography>
										<Typography
											className={twJoin(
												'text-sm',
												rop?.isMigration ? 'text-free-state-success' : 'text-free-state-error'
											)}>
											{rop?.isMigration ? 'Oui' : 'Non'}
										</Typography>
									</div>
								)}
							</div>
						</div>
					))
				) : (
					<div className='flex items-center justify-center h-36'>
						<NotAvailable />
					</div>
				)}
			</div>
		);
	}

	return <Skeleton className='w-1/2 h-36 rounded-2xl' />;
}
