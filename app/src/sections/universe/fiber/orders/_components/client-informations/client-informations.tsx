import { forwardRef, type ForwardRefExoticComponent, type RefAttributes, useState } from 'react';
// tanstack
import { useQuery } from '@tanstack/react-query';
// utils
import { Typography } from '@tools/reactore';
import { twJoin } from 'tailwind-merge';
// api
import { queries } from '@/api/queries';
// hooks
import { useOrder } from '@/hooks/queries/use-order';
// sections
import CompanyInformations from './company-informations';
import FiberLines from './fiber-lines';
import LinkedContacts from './linked-contacts';
// components
import Skeleton from '@/components-old/reactore/tou-migrate/skeleton';
import TabPanel from '@/components-old/ui/tabs/tab-panel';
import Tabs, { type TTabLabels } from '@/components-old/ui/tabs/tabs';

// ----------------------------------------------------------------------

const ClientInformations: ForwardRefExoticComponent<RefAttributes<HTMLElement>> = forwardRef<HTMLElement>((_, ref) => {
	const [tab, setTab] = useState(0);
	const statePage = useState(1);
	const [pageOtherOrder] = statePage;

	const { order, companyContacts, corpContacts, retailerContacts } = useOrder();

	const { data: companyOrders } = useQuery({
		...queries.fiber_company.byId(order?.company?.['@id'])._ctx.orders({
			page: pageOtherOrder,
			itemsPerPage: 10,
			'order[createdAt]': 'desc'
		}),
		enabled: Boolean(order?.company)
	});
	// const { data: fleets } = useQuery({
	// 	...queries.cell_company.byId(order?.company?.['@id'])._ctx.fleets,
	// 	enabled: Boolean(order?.company)
	// })

	const isCompanyOrders = companyOrders && companyOrders['hydra:member'].length;

	const tabLabels: TTabLabels = [
		{ title: 'Entreprise' },
		{ title: 'Contacts liés', quantity: companyContacts?.length, disabled: !companyContacts?.length },
		{
			title: 'Lignes fibres',
			quantity: companyOrders && companyOrders['hydra:totalItems'] - 1,
			disabled: !isCompanyOrders
		}
		// {
		// 	title: "Flottes de l'entreprise",
		// 	quantity: fleets?.['hydra:totalItems'],
		// 	disabled: !fleets?.['hydra:totalItems']
		// }
	];

	if (order) {
		return (
			<section id='infos' ref={ref}>
				<div className='flex items-end mb-2'>
					<Typography type='h5' fontFamily='iliad' className='mr-6 text-xl font-normal text-gray-400 dark:text-white'>
						Informations client
					</Typography>
				</div>
				<div className='p-6 card rounded-2xl'>
					<div className='flex justify-between'>
						<Tabs labels={tabLabels} activeTab={tab} onChange={(tab) => setTab(tab)} />
						{order?.company?.score && (
							<TabPanel activeTab={tab} index={0}>
								<div className='flex whitespace-nowrap child:ml-4'>
									{/* <span className='badge-purple h-fit'>{order.contractType}</span> */}
									<span
										className={twJoin(
											'badge-base h-fit',
											order.company.score === 'A' && 'bg-teal-200 text-teal-800',
											order.company.score === 'B' && 'bg-emerald-200 text-emerald-800',
											order.company.score === 'C' && 'bg-green-200 text-green-800',
											order.company.score === 'D' && 'bg-lime-200 text-lime-800',
											order.company.score === 'E' && 'bg-amber-200 text-amber-800'
										)}>
										Score - {order.company.score}
									</span>
								</div>
							</TabPanel>
						)}
					</div>
					<div>
						{order?.company && (
							<TabPanel activeTab={tab} index={0}>
								<CompanyInformations company={order.company} order={order} />
							</TabPanel>
						)}
						<TabPanel activeTab={tab} index={1}>
							<LinkedContacts company={companyContacts} corp={corpContacts} retailer={retailerContacts} />
						</TabPanel>
						{isCompanyOrders && (
							<TabPanel activeTab={tab} index={2}>
								<FiberLines orders={companyOrders} statePage={statePage} />
							</TabPanel>
						)}
					</div>
				</div>
			</section>
		);
	}

	return (
		<section ref={ref}>
			<div className='flex items-end mb-2'>
				<Typography type='h5' fontFamily='iliad' className='mr-6 text-xl font-normal text-gray-400 dark:text-white'>
					Informations client
				</Typography>
			</div>
			<Skeleton className='w-full h-64 rounded-2xl' />
		</section>
	);
});
export default ClientInformations;
