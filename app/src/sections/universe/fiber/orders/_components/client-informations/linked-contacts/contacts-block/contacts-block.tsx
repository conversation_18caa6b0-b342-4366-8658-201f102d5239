// utils
import { translateCompanyContactType } from '@/utils/common.utils';
import { twJoin } from 'tailwind-merge';
// api
import type { TFiberCompanyContactType } from '@/api/interface/fiber';
import type { TFiberContact } from '@/api/interface/fiber';

// ----------------------------------------------------------------------

type Props = {
	type: 'company' | 'retailer' | 'corp';
	contacts?: TFiberContact[];
};
export default function ContactsBlock({ type, contacts }: Props) {
	const tooltipEnum = {
		company: 'Contact client (Company)',
		retailer: 'Contact revendeur (Retailes)',
		corp: 'Contact interne Free (Corp)'
	};

	return (
		<>
			{contacts?.map((contact) => {
				return (
					<div className='child:color-normal flex gap-x-2 text-sm'>
						<div className='basis-1/5'>
							<div
								title={tooltipEnum[type]}
								className={twJoin(
									'typo-10 mr-1 inline-block w-full max-w-4 rounded text-center font-black text-white',
									type === 'company' && 'bg-amber-500',
									type === 'retailer' && 'bg-blue-500',
									type === 'corp' && 'bg-red-500'
								)}>
								{type === 'company' && 'C'}
								{type === 'retailer' && 'R'}
								{type === 'corp' && 'F'}
							</div>
							{'type' in contact && translateCompanyContactType(contact.type as TFiberCompanyContactType)}
						</div>
						<div className='basis-1/5 capitalize'>
							{contact?.firstName?.toLowerCase()} {contact?.lastName?.toLowerCase()}
						</div>
						<div className='basis-1/5'>{contact.email || 'N/A'}</div>
						<div className='basis-1/5'>
							{contact?.phone && `${contact.phone}`}
							{'phone2' in contact && (contact?.phone ? ` / ${contact.phone2}` : `${contact.phone2}`)}
							{!contact?.phone && !('phone2' in contact) && 'N/A'}
						</div>
						<div
							className={twJoin(
								'max-w-25',
								'comment' in contact && contact?.comment && contact.comment.length > 25 && 'truncate'
							)}
							// TMP: en attendent la nouvelle versions de tooltip reactore (PT-250)
							title={'comment' in contact && contact?.comment && contact.comment.length > 25 ? contact.comment : undefined}
						>
							{'comment' in contact && contact?.comment ? contact?.comment : 'N/A'}
						</div>
					</div>
				);
			})}
		</>
	);
}
