import { useState } from 'react';
// utils
import { zodResolver } from '@hookform/resolvers/zod';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>po<PERSON> } from '@tools/reactore';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
// locales
import { REQUIRED_FIELD } from '@/lib/i18n/constante.i18n';
// sections
import AppointmentAvailabilities from '../../appointment-schedule/create-appointment-modal/appointment-availabilities';
import CreateAppointmentValidation from '../../appointment-schedule/create-appointment-modal/create-appointment-validation';
// components
import { BellIcon } from '@/components-old/ui/icon';
// types
import type { TBaseModalProps } from '@/types/modal.interface';

// ----------------------------------------------------------------------

export type TPostOrderAppointmentScheduleData = {
	grdv_user_id: number[];
	date: string;
	startAt: string;
	duration: number;
};

const schema = z.object({
	grdv_user_id: z.array(z.number()),
	date: z.string().min(1, REQUIRED_FIELD),
	startAt: z.string().min(1, REQUIRED_FIELD),
	duration: z.number()
});

export default function CreateAppointmentModal({ isOpen, onClose }: TBaseModalProps) {
	const [isValidationTab, setIsValidationTab] = useState(false);

	const formMethods = useForm<TPostOrderAppointmentScheduleData>({
		defaultValues: { grdv_user_id: [11], duration: 60 },
		resolver: zodResolver(schema)
	});

	// const { watch } = formMethods

	const handleSave = () => {
		setIsValidationTab(true);
	};

	return (
		<Modal
			// classNameContainerChildren={twJoin(
			// 	' flex flex-col max-h-216 relative overflow-hidden transition-all',
			// 	tab === 0 && 'w-[1050px]',
			// 	tab === 1 && 'w-[1320px]'
			// )}
			// classNameContainer={twJoin(
			// 	' w-full h-full max-h-full transition-all',
			// 	tab === 0 && 'max-w-[1050px]',
			// 	tab === 1 && 'max-w-[1320px]'
			// )}
			className='z-60'
			size={isValidationTab ? 'md' : 'lg'}
			isOpen={isOpen}
			onClose={onClose}>
			<ModalHeader
				title='Prendre rendez-vous'
				hasCloseButton
				// onClose={onClose}
			/>
			{isValidationTab ? (
				<CreateAppointmentValidation formMethods={formMethods} setIsValidationTab={setIsValidationTab} onClose={onClose} />
			) : (
				<>
					<ModalBody className='flex-1 w-full p-0 desktop:p-0'>
						<AlertBanner type='alert' className=''>
							<Typography className='w-full text-center text-free-state-error'>Bientôt disponible</Typography>
						</AlertBanner>
						<AppointmentAvailabilities formMethods={formMethods} />
					</ModalBody>
					<ModalFooter className='flex justify-end'>
						<Button variant='outlined'>
							<BellIcon className='mr-2' />
							Demande de rappel
						</Button>
						<Button
							onClick={handleSave}
							className='ml-4'
							variant='contained'
							// disabled={!(watch('startAt') && watch('date'))}
							disabled
						>
							Enregistrer le RDV
						</Button>
					</ModalFooter>
				</>
			)}
		</Modal>
	);
}
