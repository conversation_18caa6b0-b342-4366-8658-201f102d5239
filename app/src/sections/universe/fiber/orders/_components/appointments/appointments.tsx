import { forwardRef, type ForwardRefExoticComponent, Fragment, type RefAttributes } from 'react';
// utils
import { Typography } from '@tools/reactore';
// hooks
import { useOrder } from '@/hooks/queries/use-order';
// sections
import AppointmentItem from './appointment-item';
// components
import Skeleton from '@/components-old/reactore/tou-migrate/skeleton';

// ----------------------------------------------------------------------

const AppointmentSchedule: ForwardRefExoticComponent<RefAttributes<HTMLElement>> = forwardRef<HTMLElement>((_, ref) => {
	const { order, appointments } = useOrder();

	// const { data: documents } = useQuery({
	// 	...queryKeys.fiber.fiber_order.byId(order?.reference)._ctx.documents,
	// 	enabled: <PERSON>olean(order)
	// })

	if (order) {
		return (
			<section ref={ref} id='appointments' className='col-span-1'>
				<div className='flex flex-col h-full'>
					<div className='flex items-end mb-2'>
						<Typography type='h5' fontFamily='iliad' className='mr-6 text-xl font-normal text-gray-400 dark:text-white'>
							Rendez-vous
						</Typography>
					</div>
					<div className='h-full py-6 card rounded-2xl'>
						<div className='flex flex-col px-6 overflow-y-auto max-h-72'>
							{appointments?.length ? (
								appointments?.map((appointment) => {
									return (
										<Fragment key={appointment['@id']}>
											<AppointmentItem appointment={appointment} />
										</Fragment>
									);
								})
							) : (
								<div className='flex items-center justify-center h-full'>
									<img
										className='w-64 mr-40'
										src='/assets/images/fiber/order/placeholder_appointment.svg'
										alt='placeholder_appointment_img'
									/>
									<div className='flex flex-col justify-center'>
										<span>Aucun rendez-vous n’a été planifié pour le moment.</span>
									</div>
								</div>
							)}
						</div>
					</div>
				</div>
			</section>
		);
	}

	return (
		<section ref={ref} id='appointmentSchedule' className='col-span-1'>
			<div className='flex flex-col h-full'>
				<div className='flex items-end mb-2'>
					<Typography type='h5' fontFamily='iliad' className='mr-6 text-xl font-normal text-gray-400 dark:text-white'>
						Rendez-vous
					</Typography>
				</div>
				<Skeleton className='h-116 rounded-2xl' />
			</div>
		</section>
	);
});
export default AppointmentSchedule;
