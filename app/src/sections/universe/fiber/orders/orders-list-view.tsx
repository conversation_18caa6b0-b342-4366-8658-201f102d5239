import { useCallback, useState } from 'react';
// tanstack
import type { OnChangeFn, VisibilityState } from '@tanstack/react-table';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
// utils
import { useAtom } from 'jotai';
import { Plus, Upload } from 'lucide-react';
// api
import type { IOrderItem } from '@/api/interface';
import { parseIri } from '@/api/fetcher';
import { queries } from '@/api/queries';
// sections
import { columns, columnsDataByKey } from './table/columns';
import { ordersFiberTableAtom, ordersFiberTableVisibilityAtom } from './table/use-orders-table';
// components
import { CustomPageHeader } from '@/components/custom';
import { DataTableDnd } from '@/components/custom-table';
import { Button } from '@/components/ui/button';

// ----------------------------------------------------------------------

type OrderType = 'ftth' | 'ftto';

interface OrdersListViewProps {
	type: OrderType;
}

const defaultFilters = {
	page: 1,
	itemsPerPage: 30,
	'order[createdAt]': 'desc' as const,
	orderFilter: ''
};

// ----------------------------------------------------------------------

export function OrdersListView({ type }: OrdersListViewProps) {
	const navigate = useNavigate({ from: '/fiber/orders' });

	const [filters, setFilters] = useState({ ...defaultFilters });

	const [tableVisibility, setTableVisibility] = useAtom(ordersFiberTableVisibilityAtom);
	const [tableOrder, setTableOrder] = useAtom(ordersFiberTableAtom);

	const { data: orders, isLoading } = useQuery({
		...queries.fiber_order.all({ ...filters }),
		placeholderData: keepPreviousData
	});

	const handleChangeFilters = useCallback((name: string, value: string) => {
		setFilters((prevFilters) => ({ ...prevFilters, [name]: value }));
	}, []);

	return (
		<>
			<CustomPageHeader title={`Liste des commandes fibres ${type === 'ftto' ? 'FTTO' : 'FTTH'}`} />

			<DataTableDnd
				linkId='orders'
				columns={columns}
				columnsDataByKey={columnsDataByKey}
				columnVisibility={{
					columnVisibility: tableVisibility,
					setColumnVisibility: setTableVisibility as OnChangeFn<VisibilityState>
				}}
				columnOrder={{ columnOrder: tableOrder, setColumnOrder: setTableOrder }}
				data={orders?.['hydra:member'] || []}
				isLoading={isLoading}
				isSearchable
				searchValue={filters.orderFilter}
				onSearch={(value) => handleChangeFilters('orderFilter', value)}
				// @ts-ignore
				onReset={() => setFilters(defaultFilters)}
				actions={
					<div className='hidden gap-2 lg:flex'>
						<Button variant='link' className='gap-2'>
							<Upload className='w-4 h-4' />
							Exporter
						</Button>
						<Button className='gap-2'>
							<Plus className='w-4 h-4' />
							Nouveau
						</Button>
					</div>
				}
				getRowId={(row) => parseIri((row as IOrderItem)['@id']) as string}
				isPaginated
				filters={[]}
				paginationState={{
					pageIndex: filters.page - 1,
					pageSize: filters.itemsPerPage
				}}
				onPaginationChange={(state) => {
					handleChangeFilters('page', (state.pageIndex + 1).toString());
					handleChangeFilters('itemsPerPage', state.pageSize.toString());
				}}
				totalRows={orders?.['hydra:totalItems']}
				// @ts-ignore
				onRowClick={(row) => navigate({ to: `/fiber${row['@id']}` })}
			/>
		</>
	);
}
