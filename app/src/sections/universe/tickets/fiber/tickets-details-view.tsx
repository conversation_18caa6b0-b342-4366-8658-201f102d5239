// api
import type { ITicketBase } from '@/api/interface'; // Assuming IFormValue type exists or adjust as needed
// sections
import Messenger from './_components/tickets-details/messenger';
// components
import CustomBadgeState from '@/components/custom/custom-badge-state';
import { AccountHeader } from '@/components/layout/account';
import { parseIri } from '@/api/fetcher';
import { Link } from '@tanstack/react-router';

// ----------------------------------------------------------------------

const getTicketReasonLabel = (reasonKey?: string): string => {
	if (!reasonKey) {
		return 'Raison inconnue';
	}

	switch (reasonKey) {
		case 'no_signal':
			return 'Pas de signal';
		case 'weak_signal':
			return 'Signal faible';
		case 'bad_signal':
			return 'Mauvais signal';
		case 'pto_move':
			return 'Déplacement PTO';
		case 'uncompliant':
			return 'Non conforme';
		case 'other':
			return 'Autre';
		default:
			return reasonKey;
	}
};

const findReasonValue = (formValues?: any[]): string | undefined => {
	if (!formValues) {
		return undefined;
	}
	const reasonFormValue = formValues.find((fv) => fv.formField?.name === 'reason');
	return reasonFormValue?.value;
};

interface TicketProps {
	ticket?: ITicketBase;
}

export default function TicketsFiberDetailsView({ ticket }: TicketProps) {
	const ticketId = ticket?.['@id'] ? parseIri(ticket['@id']) : 'N/A';
	const reasonKey = findReasonValue(ticket?.formValues);
	const reasonLabel = getTicketReasonLabel(reasonKey);
	const titleText = `SAV FTTH - #${ticketId} - ${reasonLabel}`;

	return (
		<div className='flex gap-4'>
			<section className='w-full h-[calc(100vh-80px)] flex flex-col gap-4'>
				<AccountHeader
					href='/tickets/fiber/'
					title={
						<div className='flex items-center'>
							<div className='text-xl font-bold'>
								{titleText}
								{' - '}
								<Link
									to={`/fiber/orders/${ticket?.reference}` as string}
									target={'_blank'}
									className={'text-primary underline'}>
									{ticket?.reference}
								</Link>
							</div>
						</div>
					}
					className='mb-0'>
					<div className='flex items-center gap-2'>
						{ticket?.state && <CustomBadgeState solid size={'lg'} stateId={ticket.state} />}
					</div>
				</AccountHeader>
				<Messenger />
			</section>
		</div>
	);
}
