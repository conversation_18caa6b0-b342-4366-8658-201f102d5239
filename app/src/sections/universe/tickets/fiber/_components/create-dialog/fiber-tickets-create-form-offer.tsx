import { type ReactElement, useEffect, useMemo, useState } from 'react';
// tanstack
import { useQueries, useQuery, type UseQueryResult } from '@tanstack/react-query';
// utils
import { pipe, putKeyInReactElementItems } from '@/utils';
import { type FieldArrayWithId, useFieldArray, useForm } from 'react-hook-form';
import { twMerge } from 'tailwind-merge';
// api
import type { IFormBlock, IFormField, IOrderItem, ITeamBase } from '@/api/interface';
import { magnetQueryKeys, pawnQueryKeys } from '@/api/queries';
// sections
import FiberTicketCreateDialogFooter from './fiber-tickets-create-dialog-footer';
import FiberTicketCreateDialogBanner from './fiber-tickets-create-dialog-baner';
// components
import { Form } from '@/components/hook-form/form-provider';
import { RHFCheckbox } from '@/components/hook-form/rhf-checkbox';
import { RHFFormConditions } from '@/components/hook-form/rhf-form-conditions';
import { RHFInputFile } from '@/components/hook-form/rhf-input-file';
import { RHFInputText } from '@/components/hook-form/rhf-input-text';
import { RHFSelect } from '@/components/hook-form/rhf-select';
import { ticketCreateFormSchema, type TTicketCreateFormSchema } from '@/api/schemas/form/magnet';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAuthContext } from '@/auth/auth-context';
import { useCreateTicket } from '@/api/mutations/magnet/ticket.mutation';
import { toast } from '@/hooks/use-toast';
import { TICKET_PRIORITY_OPTIONS } from '@/api/enums';
import { capitalize } from 'lodash';
import { getTicketSAVEnumsCap } from '@/api/enums/deliver/ticket-sav.enum';

// ----------------------------------------------------------------------

type TFormFieldConditions = { '@id': string; type: string };

type TFormField = { formField: string; value: string | boolean; tmp: { required: boolean; formFieldConditions: TFormFieldConditions[] } };

type TFormFieldId = FieldArrayWithId<
	{
		details: TFormField[];
	},
	'details',
	'id'
>;

type TFormatPosition = {
	position: number | string;
	component: ReactElement;
};
type Props = {
	fiberOrder?: IOrderItem;
	setOpen: (value: boolean) => void;
};

export default function FiberTicketCreateFormOffer({ fiberOrder, setOpen }: Props) {
	const { userDetails } = useAuthContext();
	const [formFields, setFormFields] = useState<TFormField[]>([]);
	const [restForm, setRestForm] = useState({});

	const [teams, setTeams] = useState<ITeamBase[]>([]);
	const [isLoading, setIsLoading] = useState(false);
	const createTicket = useCreateTicket();

	const onSubmit = (data: TTicketCreateFormSchema) => {
		setIsLoading(true);

		const processedData = {
			...data,
			details: data.details.map((detail) => {
				const formField = getFormField(detail.formField);

				if (detail.formField === '/form_fields/1' && fiberOrder?.reference) {
					return {
						...detail,
						value: fiberOrder.reference
					};
				}

				if (
					formField?.type === '/form_field_type_enums/INTEGER' &&
					typeof detail.value === 'string' &&
					!isNaN(Number(detail.value))
				) {
					return {
						...detail,
						value: Number(detail.value)
					};
				}

				return detail;
			})
		};

		createTicket.mutate(processedData, {
			onSuccess: () => {
				toast({
					title: 'Ticket',
					description: 'Ticket créé!',
					variant: 'success'
				});
				setIsLoading(false);
				setOpen(false);
			},
			onError: (error: any) => {
				if (error.response.data?.['hydra:description']?.includes('An inquiry already exists for this order')) {
					toast({
						title: 'Erreur',
						description: 'Un ticket existe déjà pour cette commande.',
						variant: 'destructive'
					});
				} else if (error.response.data?.['hydra:description']?.includes('The associated order is not done')) {
					toast({
						title: 'Erreur',
						description: "La commande associée n'as pas le bon status.",
						variant: 'destructive'
					});
				} else {
					toast({
						title: 'Ticket',
						description: 'Erreur lors de la création du ticket!',
						variant: 'error'
					});
				}
				setIsLoading(false);
			}
		});
	};

	const methods = useForm<TTicketCreateFormSchema>({
		resolver: zodResolver(ticketCreateFormSchema),
		defaultValues: {
			details: formFields,
			...restForm
		},
		values: {
			details: formFields,
			...restForm
		}
	});

	const { data: staff } = useQuery({
		...pawnQueryKeys.pawn_staff.byId(userDetails?.iri),
		enabled: Boolean(userDetails?.iri)
	});

	const { control, handleSubmit, setValue, getValues } = methods;

	const { fields } = useFieldArray({
		control,
		name: 'details'
	});

	const { data: formData } = useQuery({
		...magnetQueryKeys.magnet_form.byId(fiberOrder?.offerOrders[0].offer?.['@id'] || ''),
		enabled: Boolean(fiberOrder?.offerOrders[0].offer?.['@id'])
	});

	const getGridSpanColDefault = formData?.col || '2';

	const queriesFormBlocks = useQueries({
		queries: (formData?.formBlocks || []).map((iri) => ({
			...magnetQueryKeys.magnet_form_block.byId(iri || ''),
			enabled: Boolean(formData?.formBlocks)
		}))
	});

	useEffect(() => {
		if (formData?.formFields) {
			setFormFields(
				formData.formFields.map((item): TFormField => {
					const formFieldConditions = item?.formFieldConditionDepends.map((formFieldCondition) => ({
						'@id': formFieldCondition['@id'],
						type: formFieldCondition.type
					}));
					const required = item?.required;
					const defaultValue = item?.formFieldConditions.find((item) => item.value !== undefined)?.value || '';
					const value = (() => {
						switch (item.type) {
							case '/form_field_type_enums/BOOLEAN':
								return defaultValue === 'true';
							default:
								return '';
						}
					})();
					return { formField: item?.['@id'] || '', value, tmp: { formFieldConditions, required } };
				})
			);
			setRestForm({ form: formData['@id'], reference: fiberOrder?.reference });
		}
	}, [formData]);

	useEffect(() => {
		if (staff) {
			setTeams(staff.teams);
		}
	}, [staff]);

	const getFormField = (fieldId: string | number): IFormField | undefined =>
		(formData?.formFields || []).find((field) => field?.['@id'] === fieldId);

	const getFormFieldIndex = (fieldId: string | number): number =>
		(formData?.formFields || []).findIndex((field) => field?.['@id'] === fieldId);

	const getFieldsIsNotFormBlock = (fields: TFormFieldId[]) =>
		fields.filter((field) => Boolean(getFormField(field.formField)?.formBlock) === false);

	const getFieldsFormBlock = (iriBlock: string) => (fields: TFormFieldId[]) =>
		fields.filter((field) => getFormField(field.formField)?.formBlock === iriBlock);

	const handleFormFieldConditions =
		(str: string) =>
		(typeConditions: TFormFieldConditions[] = []) => {
			if (typeConditions.length > 0)
				for (const item of typeConditions) {
					if (item.type === str) return false;
				}
			return true;
		};

	const chooseFormFieldType = (field: TFormField & { id: string }, formField: IFormField | undefined, index: number) => {
		const handleChange = () => {
			const formFieldConditions = formField?.formFieldConditions || [];
			if (formFieldConditions && formFieldConditions.length > 0) {
				for (const item of formFieldConditions) {
					item.formFieldChilds.forEach((itemChild) => {
						const field: any = getValues(`details.${getFormFieldIndex(itemChild?.['@id'])}`);
						if (field) {
							const indexMustBeRemove = field.tmp.formFieldConditions.findIndex(
								(itemConditions: any) => item['@id'] === itemConditions['@id']
							);
							const formFieldConditions = field.tmp.formFieldConditions.filter(
								(_: any, index: number) => indexMustBeRemove !== index
							);
							if (indexMustBeRemove === -1)
								setValue(`details.${getFormFieldIndex(itemChild?.['@id'])}`, {
									formField: field?.formField || '',
									value: '',
									tmp: {
										...field.tmp,
										formFieldConditions: [...field?.tmp.formFieldConditions, { '@id': item['@id'], type: item.type }]
									}
								});
							else
								setValue(`details.${getFormFieldIndex(itemChild?.['@id'])}`, {
									...field,
									value: '',
									tmp: { ...field.tmp, formFieldConditions }
								});
						}
					});
				}
			}
		};
		//
		const RHFFormFieldShosen = (() => {
			// if (formField?.inputType === '/form_field_type_front_enums/SELECT')
			// 	console.log(formField?.label, ' ',getTicketSAVEnums(formField.enum || []));

			switch (formField?.inputType) {
				case '/form_field_type_front_enums/SELECT':
					return (
						formField?.enum && (
							<RHFSelect
								name={`details.${index}.value`}
								label={formField?.label}
								items={getTicketSAVEnumsCap(formField.enum || [])}
							/>
						)
					);
				case '/form_field_type_front_enums/CHECKBOX':
					return <RHFCheckbox name={`details.${index}.value`} label={formField?.label} callFnInOnChange={handleChange} />;
				case '/form_field_type_front_enums/FILE':
					return <RHFInputFile name={`details.${index}.value`} label={formField?.label} />;
				case '/form_field_type_front_enums/TEXT':
					return <RHFInputText name={`details.${index}.value`} label={formField?.label} />;
				default:
					return null;
			}
		})();

		return (
			<RHFFormConditions
				key={field.id}
				name={`details.${index}.tmp.formFieldConditions`}
				shouldHidden={handleFormFieldConditions('hidden')}>
				{RHFFormFieldShosen}
			</RHFFormConditions>
		);
	};

	const handleSortPosition = (data: TFormatPosition[]): TFormatPosition[] => {
		data.sort((a: TFormatPosition, b: TFormatPosition) => Number(a.position) - Number(b.position));
		return data;
	};

	const handleSortedDataRender = (data: TFormatPosition[]) => data.map((item: TFormatPosition) => item.component);

	const formatPositionFields = (fields: TFormFieldId[]): TFormatPosition[] =>
		fields
			// Filter out the field with ID "/form_fields/1" since it will be set automatically
			.filter((field) => field.formField !== '/form_fields/1')
			.map((field: TFormFieldId) => ({
				position: getFormField(field.formField)?.position || 0,
				component: (
					<div className={`col-span-${getFormField(field.formField)?.col || getGridSpanColDefault}`}>
						{chooseFormFieldType(field, getFormField(field.formField), getFormFieldIndex(field.formField))}
					</div>
				)
			}));

	const resultFormFields = useMemo(() => pipe(getFieldsIsNotFormBlock, formatPositionFields)(fields), [fields]);

	// FORMAT FORM BLOCKS

	const pipeTypeGetFieldBlocks = (iriBlock: string) =>
		pipe(
			getFieldsFormBlock(iriBlock),
			(blockFields: TFormFieldId[]) => blockFields.filter((field) => field.formField !== '/form_fields/1'),
			formatPositionFields,
			handleSortedDataRender,
			putKeyInReactElementItems
		)(fields);

	const formatPositionFormBlocks = (formBlock: IFormBlock, index: number): TFormatPosition => ({
		position: formBlock?.position || 0,
		component: (
			<div
				key={`formBlocks${formBlock?.['@id']}-${index}`}
				className={twMerge(
					'flex flex-col rounded-2xl border-gray-200 dark:border-neutral-700 border-[1px] p-4 gap-3',
					`col-span-${formBlock.col || 1}`
				)}>
				<div>
					<div className='text-[18px] font-bold'>{formBlock.title}</div>
					<div className='text-gray-400 text-3'>{formBlock.secondTitle}</div>
				</div>
				{pipeTypeGetFieldBlocks(formBlock?.['@id'])}
			</div>
		)
	});

	const getDataFormBlocks = (dataFormBlocks: UseQueryResult<IFormBlock, Error>[]) =>
		dataFormBlocks.map((dataFormBlock, index) =>
			dataFormBlock.data ? formatPositionFormBlocks(dataFormBlock.data, index) : undefined
		);

	const resultFormBlocks = useMemo(() => getDataFormBlocks(queriesFormBlocks).filter((item) => item), [queriesFormBlocks]);

	const render = useMemo(
		() => pipe(handleSortPosition, handleSortedDataRender, putKeyInReactElementItems)([...resultFormFields, ...resultFormBlocks]),
		[resultFormBlocks, resultFormFields]
	);

	return (
		<div className='flex flex-col flex-1 gap-3'>
			<FiberTicketCreateDialogBanner orderFiber={fiberOrder} />
			<div className='relative flex flex-1'>
				<Form
					methods={methods}
					className='p-0.5 absolute w-full overflow-y-scroll space-y-3 h-full'
					onSubmit={handleSubmit(onSubmit)}>
					<div className={`grid grid-cols-${getGridSpanColDefault} gap-3`}>
						{teams.length > 0 && (
							<RHFSelect
								name={`team`}
								label={'Team'}
								placeholder={'Sélectionnez une équipe'}
								items={teams.map((team) => ({
									label: team?.team?.name,
									value: team?.team?.['@id']
								}))}
							/>
						)}
						<RHFSelect
							name={'priority'}
							label={'Priorité'}
							placeholder={'Sélectionnez une priorité'}
							items={TICKET_PRIORITY_OPTIONS().map((item) => ({
								label: capitalize(item.label),
								value: item?.['@id']
							}))}
						/>
						{render}
						<div className={`col-span-${getGridSpanColDefault}`}>
							<FiberTicketCreateDialogFooter submitTitle='Créer mon ticket' isLoading={isLoading} />
						</div>
					</div>
				</Form>
			</div>
		</div>
	);
}
