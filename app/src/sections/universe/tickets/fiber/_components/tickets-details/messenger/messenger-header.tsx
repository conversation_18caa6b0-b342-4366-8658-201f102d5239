import { useEffect, useMemo } from 'react';
// tanstack
import { useQueries } from '@tanstack/react-query';
import { useParams } from '@tanstack/react-router';
import { format } from 'date-fns';
// api
import type { IPawnTeam } from '@/api/schemas/entities';
import { magnetQueryKeys } from '@/api/queries';
import { useMagnetTicketQuery } from '@/api/queries/magnet/useMagnetTicket.query.ts';
import { usePawnStaffQuery } from '@/api/queries/pawn/usePawnStaff.query.ts';
// others
import useTicketAtom from '../ticket.atom';
import TimeElapsedWithGTR from '@/sections/universe/tickets/fiber/_components/tickets-details/messenger/GTRBadge.tsx';
import type { ITicketMessage } from '@/api/interface';
import { useTeamData, getTeamName } from '@/components/fetchers/team.ts';
import { parseIri } from '@/api/fetcher';
import TeamAvatarTooltip from '@/sections/universe/tickets/fiber/_components/tickets-details/messenger/teams-display.tsx';
import { Badge } from '@/components/ui/badge.tsx';

// ----------------------------------------------------------------------

export default function MessengerHeader() {
	const { id } = useParams({
		strict: false
	});

	const { teams } = usePawnStaffQuery();
	const { ticket, order } = useMagnetTicketQuery(id);

	const creatorTeamId = ticket?.user?.team ? (parseIri(ticket.user.team as string) as string) : '';
	const creatorTeamData = useTeamData(creatorTeamId);

	const activeOutsources = useQueries({
		queries: ticket?.activeOutsources?.length
			? ticket?.activeOutsources?.map((activeOutsourceItem: any) => {
					return {
						...magnetQueryKeys.magnet_outsource.byId(activeOutsourceItem['@id']),
						enabled: Boolean(ticket && activeOutsourceItem['@id'])
					};
				})
			: []
	});
	const [ticketParams, setTicketParams] = useTicketAtom();

	const { user: userCreator, createdAt } = ticket || {};
	const { team: userTeam } = userCreator || {};

	useEffect(() => {
		if (teams?.length && userTeam && !ticketParams?.team) {
			const teamFind = teams.find((teamItem) => {
				if (typeof teamItem.team === 'string') {
					return userTeam === teamItem.team;
				}

				return userTeam === teamItem.team?.['@id'];
			});
			const selectCurrentTeam = teamFind?.team as unknown as IPawnTeam;

			if (selectCurrentTeam) {
				setTicketParams({
					...ticketParams,
					team: selectCurrentTeam['@id']
				});
			}
		}
	}, [userTeam]);

	const _outsources = activeOutsources.filter(({ data }) => data).map(({ data }) => data?.name);

	function getUniqueTeamsFromMessages(messages: ITicketMessage[]): string[] {
		const uniqueTeamIds = new Set<string>();

		messages.forEach((message) => {
			if (message.user && message.user.team) {
				uniqueTeamIds.add(message.user.team);
			}
		});

		return Array.from(uniqueTeamIds);
	}

	const uniqueTeamIds = useMemo(() => getUniqueTeamsFromMessages(ticket?.messages || []), [ticket?.messages]);
	return (
		<section
			className='flex h-14 justify-between rounded-t-2xl bg-card px-4 py-3 shadow-xl
 text-foreground whitespace-nowrap overflow-x-scroll z-10'>
			<div className='flex items-center'>
				<div className={'flex items-center '}>
					{uniqueTeamIds.map((teamId, index) => (
						<TeamAvatarTooltip key={teamId} teamId={teamId} index={index} />
					))}
				</div>

				<div className='flex items-center text-sm ml-3 mr-2'>
					Ouvert par
					<span className='font-semibold mx-1'>{getTeamName(creatorTeamData)}</span>
					le
					{createdAt ? format(createdAt, ' dd/MM/yyyy à HH:mm') : 'N/A'}
					{ticket?.state !== '/ticket_state_enums/SOLVED' && (
						<TimeElapsedWithGTR createdAt={createdAt} gtrGtiString={order?.sla} className='ml-2 py-1' />
					)}
				</div>
			</div>
			<div className='flex items-center text-sm'>
				{_outsources.map((_outsource, index) => (
					<span key={index} className='font-semibold mx-1 text-[#275ECB]'>
						{ticket?.ticketOutsources?.[0].context?.gtaId
							? `GTA #${ticket?.ticketOutsources?.[0].context?.gtaId}`
							: 'Syncronisation GTA en cours'}{' '}
						{ticket?.ticketOutsources?.[0].context?.gtaId && (
							<OutsourceItem outsourceState={ticket?.ticketOutsources?.[0].state as string} />
						)}
					</span>
				))}
			</div>
		</section>
	);
}

function OutsourceItem({ outsourceState }: { outsourceState: string }) {
	const label = (() => {
		switch (outsourceState) {
			case 'created':
				return 'Ouvert';
			case 'open':
				return 'En cours';
			case 'closure_requested':
				return 'Cloture demandée';
			case 'closed':
				return 'Cloturé';
			case 'cancelled':
				return 'Annulé';
			default:
				return outsourceState;
		}
	})();

	const variant = (() => {
		switch (outsourceState) {
			case 'created':
				return 'blue';
			case 'open':
				return 'green';
			case 'closure_requested':
				return 'orange';
			case 'closed':
				return 'green';
			case 'cancelled':
				return 'red';
			default:
				return 'secondary';
		}
	})();

	return <Badge variant={variant}>{label}</Badge>;
}
