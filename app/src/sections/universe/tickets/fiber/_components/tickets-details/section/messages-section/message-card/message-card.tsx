// tanstack
import { useQuery } from '@tanstack/react-query';
// utils
import HTMLReactParser from 'html-react-parser';
import { formatDate } from '@tools/reactore';
import { templateSettings } from 'lodash';
import { twJoin } from 'tailwind-merge';
// api
import type { IMagnetMessage } from '@/api/interface/magnet';
import { pawnQueryKeys } from '@/api/queries';

// ----------------------------------------------------------------------

type Props = {
	isResponse: boolean;
	message: IMagnetMessage;
};

templateSettings.interpolate = /{{([\s\S]+?)}}/g;

export default function MessageCard({ isResponse, message }: Props) {
	const { createdAt, user } = message;
	const { data: teamData } = useQuery({
		...pawnQueryKeys.pawn_team.byId(user.team),
		enabled: <PERSON><PERSON><PERSON>(user.team)
	});

	let { content } = message;

	// @ts-ignore
	const username = user?.createdByName;

	const messageText = content.replaceAll('\n', '<br/>');

	const regExp = /<[a-zA-Z0-9_@.]+>/g;
	const messageMatcheRegExp = content?.matchAll(regExp);
	//
	//
	if (messageMatcheRegExp) {
		[...messageMatcheRegExp].forEach((data) => {
			if (data[0] !== '<div>') {
				const dataReplace = data[0].replaceAll(/([<>])/g, '');
				content = content?.replace(data[0], dataReplace);
			}
		});
		content = content?.replace(/(\r\n|\n)/g, '<br />');
	}

	return (
		<div className={twJoin(`mb-6 flex snap-end flex-col items-${isResponse ? 'end' : 'start'}`)}>
			<div className={twJoin('flex w-1/2 justify-between', isResponse && 'flex-row-reverse')}>
				<p className={twJoin('mb-2 text-gray-500')}>
					<b className='text-sm text-free-black dark:text-gray-200'>{username} </b>
					{createdAt && <span className='text-sm'>- {formatDate(new Date(createdAt), 'dd/MM/yyyy à HH:mm') as string}</span>}
				</p>
				<div
					className={twJoin(
						'h-fit rounded-md px-3 py-1 text-xs font-semibold text-black',
						isResponse ? 'bg-red-500' : 'bg-purple-400 '
					)}>
					{/* {isResponse ? 'Helper #' : 'GTA #'} */}
					{teamData && teamData.name}
				</div>
			</div>
			<div
				className={twJoin(
					'dark:border-dark-smoke dark:bg-dark-smoke-light w-1/2 rounded-xl border border-gray-200 bg-white px-5 pb-5 pt-3 text-sm link:text-blue-300 link:underline',
					isResponse ? 'rounded-tr-none' : 'rounded-tl-none'
				)}>
				{messageText && (
					<>{HTMLReactParser(content)}</>
					// <Typography>{HTMLReactParser(messageText)}</Typography>
				)}
			</div>
		</div>
	);
}
