// tanstack
import { useQueryClient } from '@tanstack/react-query';
import { useParams } from '@tanstack/react-router';
// utils
import { zodResolver } from '@hookform/resolvers/zod';
import { ApiErrors, Button, Input, Modal, Modal<PERSON>ody, <PERSON><PERSON><PERSON><PERSON>er, Modal<PERSON>eader } from '@tools/reactore';
import { useForm } from 'react-hook-form';
import { type z, object, string } from 'zod';
// api
import { magnetFetcher } from '@/api/fetcher';
import { magnetQueryKeys } from '@/api/queries';
// hooks
import useMutation from '@/hooks/use-mutation';
// locales
import { REQUIRED_FIELD } from '@/lib/i18n/constante.i18n';
// types
import type { TBaseModalProps } from '@/types/modal.interface';

// ----------------------------------------------------------------------

const schema = object({
	closeComment: string().min(1, REQUIRED_FIELD)
});
type TCloseTicketSchema = z.infer<typeof schema>;

export default function CloseTicketModal({ isOpen, onClose }: TBaseModalProps) {
	const queryClient = useQueryClient();

	const { id } = useParams({ strict: false });

	const formMethods = useForm<TCloseTicketSchema>({
		resolver: zodResolver(schema)
	});
	const { handleSubmit } = formMethods;

	const awaitingClosureTicketMutation = useMutation<TCloseTicketSchema, TCloseTicketSchema>({
		mutationFn: (data) => magnetFetcher({ url: `/exchanges/tickets/${id}/answer`, data, method: 'patch' }),
		onSuccess: () => {
			queryClient
				.invalidateQueries({
					queryKey: magnetQueryKeys.magnet_ticket.byId(id).queryKey
				})
				.then(() => {
					onClose();
				});
		}
	});

	const onSubmit = (data: TCloseTicketSchema) => {
		awaitingClosureTicketMutation.mutate(data);
	};

	const handleClose = () => {
		awaitingClosureTicketMutation.reset();
		onClose();
	};

	return (
		<Modal isOpen={isOpen} onClose={handleClose}>
			<ModalHeader title='Clôturer le ticket' hasCloseButton />
			<ModalBody>
				<form id='close_ticket-form' onSubmit={handleSubmit(onSubmit)} className='mb-4 flex flex-col'>
					<Input
						label='Raison de la clôture'
						type='select'
						placeholder='Sélectionner la raison'
						name='closeComment'
						formMethods={formMethods}
						selectOptions={{
							items: [
								{ value: 'resolve', label: 'résolut' },
								{ value: 'close_bug_box_active', label: 'clôturer: problème Box actif' },
								{ value: 'close_bug_infra_client', label: 'clôturer: problème client infra' },
								{ value: 'close_bug_onu_active', label: 'clôturer: problème ONU actif' },
								{ value: 'close_repair_infra_exterior', label: "cloturer: réparer l'infra extérieur" },
								{ value: 'reject_bad_client', label: 'rejeter: mauvais client' },
								{ value: 'reject_bad_diagnostic', label: 'rejeter: mauvais diagnostique' },
								{ value: 'reject_box_work', label: ' rejeter: box installer' },
								{ value: 'reject_line_not_declare', label: ' rejeter: ligne non déclarée' },
								{ value: 'reject_link_up', label: 'rejeter: link up ' },
								{ value: 'other', label: 'autre' }
							]
						}}
					/>
				</form>
				<div className='text-center'>
					{/* @ts-ignore: ts temp */}
					<ApiErrors errors={[awaitingClosureTicketMutation.error]} />
				</div>
			</ModalBody>
			<ModalFooter className='flex justify-end space-x-4'>
				<Button variant='outlined' onClick={handleClose}>
					Annuler
				</Button>
				<Button form='close_ticket-form' variant='contained' type='submit'>
					Valider
				</Button>
			</ModalFooter>
		</Modal>
	);
}
