// utils
import { cn } from '@/lib/utils';
import { Download01 } from '@tools/reactor-icons';
import { useDrop } from 'react-dnd';
import { NativeTypes } from 'react-dnd-html5-backend';

// ----------------------------------------------------------------------

type Props = {
	accept?: '*/*';
	className?: string;
	onDrop: (data: {
		files: File[];
	}) => void;
	onRemoveFile: (index: string) => void;
	files: [
		string,
		{
			file: File;
			id?: string;
			error?: string;
		}
	][];
};

export default function DropFile({ className, onDrop }: Props) {
	// console.log(data)
	const [, drop] = useDrop(
		() => ({
			accept: [NativeTypes.FILE],
			drop(item: {
				files: File[];
			}) {
				if (onDrop) {
					onDrop(item);
				}
			},
			// canDrop(item) {
			// 	console.log('canDrop', item)
			// 	return true
			// },
			// hover(item) {
			// 	console.log('hover', item)
			// },
			collect: (monitor) => {
				return {
					isOver: monitor.isOver(),
					canDrop: monitor.canDrop()
				};
			}
		}),
		[onDrop]
	);

	return (
		<section
			className={cn('h-full w-full rounded-xl border border-accent-foreground/40 border-dashed bg-accent-foreground/10', className)}
			ref={drop as unknown as React.Ref<HTMLElement> | undefined}>
			<input
				onChange={({ target }) => {
					const { files: fileList } = target;
					if (fileList && fileList.length) {
						onDrop({
							files: [...fileList]
						});
					}
				}}
				type='file'
				multiple={true}
				accept='*/*'
				id='actual-btn'
				hidden
			/>
			<label htmlFor='actual-btn' className='flex items-center justify-center w-full h-full cursor-pointer text-foreground/50'>
				<Download01 className='mr-2 h-4.5 w-4.5' />
				<span className='text-sm'>Déposez votre document ici ou cliquez pour ajouter</span>
			</label>
		</section>
	);
}
