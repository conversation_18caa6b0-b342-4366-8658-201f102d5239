// utils
import { ChevronUp, Grid01, RIcon, type TNameIcons } from '@tools/reactor-icons';
// api
import type { IMagnetOutSource } from '@/api/schemas';
import { getOutsourceTypeValueById } from '@/api/enums/magnet/outsource_type.enum.ts';
// components
import { Checkbox } from '@/components/ui/checkbox.tsx';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu.tsx';
// others
import useTicketAtom from '../../../ticket.atom';

// ----------------------------------------------------------------------

type Props = {
	outsources: (IMagnetOutSource | undefined)[];
};

export default function OutsourceSelectDropdown({ outsources }: Props) {
	const [ticketParams, setTicketParams] = useTicketAtom();

	return (
		<DropdownMenu>
			<DropdownMenuTrigger className='group relative flex h-7.5 w-15.5 items-center justify-center rounded-4xl border-solid bg-[#F3F4FC] px-2.5 py-1.5 data-[state=open]:bg-[#E2E4F5] [&>svg[data-svg="chevron"]]:data-[state=open]:rotate-180'>
				{ticketParams.outsources && Object.values(ticketParams.outsources).find((value) => value) && (
					<div className='absolute top-1 right-0.75 z-20 h-1.75 w-1.75 rounded-xl bg-red-700' />
				)}
				<Grid01 className='mr-2 h-4.5' />
				<ChevronUp data-svg='chevron' />
			</DropdownMenuTrigger>
			<DropdownMenuContent align='end' className='rounded-xl' side='top'>
				{outsources.map((outsourceItem) => {
					const { name, '@id': id } = outsourceItem || {};
					const type = getOutsourceTypeValueById(outsourceItem?.type);

					let icon: TNameIcons | undefined;

					switch (type) {
						case 'mailing_list':
							icon = 'Mail02';
							break;
						default:
					}

					return (
						<DropdownMenuItem
							key={id}
							className='text-sm'
							onClick={(e) => {
								e.preventDefault();

								setTicketParams((prev) => {
									const prevOutsources = prev?.outsources || {};
									const nextOutsources: Record<string, boolean> = {};

									if (id) {
										nextOutsources[id] = !prevOutsources?.[id];
									}

									return {
										...prev,
										outsources: {
											...prevOutsources,
											...nextOutsources
										}
									};
								});
							}}>
							{icon && <RIcon name={icon} className='h-4.5' />} {name}{' '}
							<Checkbox
								className='border-[#D4D4D4] data-[state=checked]:border-purple-700 data-[state=checked]:bg-white data-[state=checked]:text-purple-700'
								checked={Boolean(id && ticketParams?.outsources?.[id])}
							/>
						</DropdownMenuItem>
					);
				})}
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
