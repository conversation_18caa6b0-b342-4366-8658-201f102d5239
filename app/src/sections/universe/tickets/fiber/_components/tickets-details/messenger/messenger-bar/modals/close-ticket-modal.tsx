// tanstack
import { useQueryClient } from '@tanstack/react-query';
import { useParams } from '@tanstack/react-router';
// utils
import { zodResolver } from '@hookform/resolvers/zod';
import { ApiErrors } from '@tools/reactore';
import { useForm } from 'react-hook-form';
import { object, string, type z } from 'zod';
import { magnetFetcher } from '@/api/fetcher';
import { useMutation } from '@/api/fetcher/mutation-service.ts';
import { magnetQueryKeys } from '@/api/queries';
// locales
import { REQUIRED_FIELD } from '@/lib/i18n/constante.i18n.ts';
// components
import { Button } from '@/components/ui/button.tsx';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog.tsx';
// types
import type { TModalProps } from '@/types';
import { Form, FormField, FormItem } from '@/components/ui/form.tsx';
import { SelectDropdown } from '@/components/select-dropdown.tsx';
import { TICKET_CLOSE_OPTIONS } from '@/api/enums';

// ----------------------------------------------------------------------

const schema = object({
	closeComment: string().min(1, REQUIRED_FIELD)
});
type TCloseTicketSchema = z.infer<typeof schema>;

export default function CloseTicketModal({ isOpen, onClose }: TModalProps) {
	const queryClient = useQueryClient();

	const { id } = useParams({
		strict: false
	});
	const form = useForm<TCloseTicketSchema>({
		resolver: zodResolver(schema)
	});
	const { handleSubmit } = form;

	const awaitingClosureTicketMutation = useMutation<TCloseTicketSchema, TCloseTicketSchema, TCloseTicketSchema>({
		mutationFn: (data: any) => magnetFetcher({ url: `/tickets/${id}/answer`, data, method: 'patch' }),
		onSuccess: () => {
			queryClient
				.invalidateQueries({
					queryKey: magnetQueryKeys.magnet_ticket.byId(id as string).queryKey
				})
				.then(() => {
					onClose();
				});
		}
	});

	const onSubmit = (data: TCloseTicketSchema) => {
		awaitingClosureTicketMutation.mutate(data);
	};

	const handleClose = (open: boolean) => {
		if (open) return;
		awaitingClosureTicketMutation.reset();
		onClose();
	};

	return (
		<>
			<Dialog open={isOpen} onOpenChange={handleClose}>
				<DialogContent className={'w-[350px]'}>
					<DialogHeader>
						<DialogTitle>Clôturer le ticket</DialogTitle>
						<DialogDescription className='pt-2'>
							<Form {...form}>
								<form id='close_ticket-form' onSubmit={handleSubmit(onSubmit)} className='mb-4 flex flex-col'>
									<FormField
										control={form.control}
										name='closeComment'
										render={({ field }) => (
											<FormItem className='grid items-center w-full'>
												<SelectDropdown
													defaultValue={field.value}
													onValueChange={field.onChange}
													placeholder='Sélectionner la raison'
													items={TICKET_CLOSE_OPTIONS()}
												/>
											</FormItem>
										)}
									/>
								</form>
							</Form>
							<div className='text-center'>
								{/* @ts-ignore: ts temp */}
								<ApiErrors errors={[awaitingClosureTicketMutation.error]} />
							</div>
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button variant='neutral' onClick={() => handleClose(false)}>
							Annuler
						</Button>
						<Button form='close_ticket-form' type='submit'>
							Valider
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
}
