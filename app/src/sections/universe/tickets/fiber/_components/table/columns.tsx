// utils
import dayjs from 'dayjs';
// api
import type { ITicketBase } from '@/api/interface';
import { parseIri } from '@/api/fetcher';
import { type ExtendedColumnDef, handleInitColumnsDataByKey } from '@/components/custom-table/data-table-dnd';
import { orderFetcher } from '@/components/fetchers/orders';
import { teamFetcher } from '@/components/fetchers/team.ts';
import CustomBadgeState from '@/components/custom/custom-badge-state.tsx';

// ----------------------------------------------------------------------

export const columns: ExtendedColumnDef<ITicketBase>[] = [
	/*	{
		id: 'select',
		accessorKey: 'select',
		title: 'Select',
		header: ({ table }) => (
			<Checkbox
				checked={table.getIsAllPageRowsSelected()}
				onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
				aria-label='Select all'
				className='translate-y-[2px]'
			/>
		),
		cell: ({ row }) => (
			<Checkbox
				checked={row.getIsSelected()}
				onCheckedChange={(value) => row.toggleSelected(!!value)}
				aria-label='Select row'
				className='translate-y-[2px]'
			/>
		),
		enableSorting: false,
		enableHiding: false,
		size: 40
	},*/
	{
		accessorKey: '@id',
		id: '@id',
		header: 'ID',
		title: 'ID',
		cell: ({ row }) => <div className='w-[80px]'>#{parseIri(row.getValue('@id'))}</div>,
		size: 70
	},
	{
		id: 'state',
		accessorKey: 'state',
		header: 'Statut',
		title: 'Statut',
		size: 70,
		cell: ({ row }) => {
			const stateId = row.getValue('state') as string;
			return <CustomBadgeState solid size={'sm'} stateId={stateId} />;
		}
	},
	{
		id: 'form.name',
		accessorKey: 'form.name',
		title: 'Intitulé',
		header: 'Intitulé',
		//header: ({ column }) => <DataTableColumnHeader column={column} title='Intitulé' />,
		cell: ({ row }) => <div className='text-nowrap'>{row.getValue('form.name')}</div>,
		size: 300
	},
	/*{
		id: 'actions',
		accessorKey: 'actions',
		header: 'Suivi',
		title: 'Suivi',
		size: 100,
		cell: () => {
			return (
				<div className='flex gap-4 text-gray-400'>
					<File04 size={19} />
					<MessageDotsCircle size={19} />
					<Attachment01 size={19} />
				</div>
			);
		}
	},
	{
		id: 'priority',
		accessorKey: 'priority',
		header: 'Priorité',
		title: 'Priorité',
		size: 100,
		cell: ({ row }) => {
			const priorityId = row.getValue('priority') as string;
			const priorityValue = getTicketPriorityValue(priorityId);

			const colorClass =
				{
					[TicketPriorityEnum.LOW]: 'text-blue-500',
					[TicketPriorityEnum.NORMAL]: 'text-yellow-500',
					[TicketPriorityEnum.HIGH]: 'text-orange-500',
					[TicketPriorityEnum.URGENT]: 'text-red-500'
				}[priorityValue as TicketPriorityEnum] || '';

			return priorityValue ? <Flag01 size={18} className={colorClass} /> : null;
		}
	},*/
	{
		id: 'user.team',
		accessorKey: 'user.team',
		header: 'Auteur',
		title: 'Auteur',
		cell: ({ row }) => {
			const author = parseIri(row.getValue('user.team')) as string;
			return <div className='text-nowrap'>{teamFetcher(author)}</div>;
		}
	},
	{
		id: 'createdAt',
		accessorKey: 'createdAt',
		header: 'Créé le',
		title: 'Créé le',
		cell: ({ row }) => {
			const date = row.getValue('createdAt') as string;
			return <div>{dayjs(date).format('DD/MM/YYYY')}</div>;
		}
	} /*
	{
		id: 'assignedTo',
		accessorKey: 'assignedTo',
		header: 'Assigné à',
		title: 'Assigné à',
		cell: ({ row }) => {
			return <div className='text-nowrap'>{row.getValue('assignedTo')}</div>;
		}
	},*/,
	{
		id: 'reference',
		accessorKey: 'reference',
		header: 'Ref commande',
		title: 'Ref commande'
	} /*
	{
		id: 'adresse',
		accessorKey: 'adresse',
		header: 'Adresse',
		title: 'Adresse',
		cell: ({ row }) => {
			const reference = row.getValue('reference') as string;
			return <div className='text-nowrap'>{orderFetcher(reference, 'address')}</div>;
		}
	},*/,
	{
		id: 'NRO',
		accessorKey: 'NRO',
		header: 'NRO',
		title: 'NRO',
		cell: ({ row }) => {
			const reference = row.getValue('reference') as string;
			return orderFetcher(reference, 'NRO');
		}
	}
];

export const columnsDataByKey = handleInitColumnsDataByKey(columns);
