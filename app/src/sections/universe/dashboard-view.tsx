// utils
import { InfoIcon } from 'lucide-react';
// components
import Typography from '@/components/ui/typography';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

// ----------------------------------------------------------------------

export default function DashboardView() {
	return (
		<div className='container py-8 space-y-8'>
			{/* Header */}
			<header className='flex flex-col gap-4 md:flex-row md:items-center md:justify-between'>
				<Typography.Title variant='3/semibold'>Mon tableau de bord</Typography.Title>
			</header>

			{/* Background image with proper z-index control */}

			<Alert className='mt-8'>
				<InfoIcon className='w-5 h-5' />
				<AlertTitle className='text-lg font-bold'>Version BETA</AlertTitle>
				<AlertDescription>
					<p className='mb-3 text-base'>
						Bienvenue sur ProUI, l'outil de gestion unifié de production fibre et mobile à destination des clients
						professionnels.
					</p>
					<p className='mb-3 text-sm'>
						L'outil est toujours en cours de développement actif par la Proteam et nous avons besoin de vos retours
						d'expériences sur l'utilisation de l'outil pour l'améliorer et le fiabiliser.
					</p>
				</AlertDescription>
			</Alert>

			{/* Dashboard content */}
			<section className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
				<Card>
					<CardHeader>
						<Typography.Title variant='6/medium'>Statistiques</Typography.Title>
					</CardHeader>
					<CardContent>
						<p className='text-sm text-muted-foreground'>Les statistiques seront bientôt disponibles.</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<Typography.Title variant='6/medium'>Activités récentes</Typography.Title>
					</CardHeader>
					<CardContent>
						<p className='text-sm text-muted-foreground'>Les activités récentes seront bientôt disponibles.</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<Typography.Title variant='6/medium'>Tâches</Typography.Title>
					</CardHeader>
					<CardContent>
						<p className='text-sm text-muted-foreground'>Les tâches seront bientôt disponibles.</p>
					</CardContent>
				</Card>
			</section>
		</div>
	);
}
