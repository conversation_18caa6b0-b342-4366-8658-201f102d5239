// utils
import Spline from '@splinetool/react-spline';
import { twJoin } from 'tailwind-merge';
// sections
import LoginSection from './_components/login-section';
import { useState } from 'react';
import ForgetPasswordSection from './_components/forgot-password-section';

// ----------------------------------------------------------------------

export default function LoginView() {
	const [section, setSection] = useState<'login' | 'forgot-password'>('login');

	const handleChangeSection = (section: 'login' | 'forgot-password') => {
		setSection(section);
	};

	return (
		<div
			className={twJoin(
				'relative h-full w-full flex lg:flex-row flex-col-reverse dark:bg-free-dark-mode dark:text-gray-100 lg:h-screen items-center'
			)}>
			<div className='lg:w-1/2 h-full flex justify-center items-center'>
				<div className='relative flex justify-center w-full px-5 lg:w-1/2 lg:px-0'>
					<div className='relative flex w-full max-w-125  flex-col items-center lg:max-w-112.5  lg:items-start'>
						{section === 'login' ? (
							<LoginSection setSection={handleChangeSection} />
						) : (
							<ForgetPasswordSection setSection={handleChangeSection} />
						)}
					</div>
				</div>
			</div>
			<div className='lg:w-1/2 w-full h-full p-5  overflow-hidden '>
				<div className='rounded-3xl overflow-hidden w-full h-full transition-opacity ease-in duration-700 opacity-100 lg:max-h-full max-h-[500px]'>
					<Spline scene='https://prod.spline.design/LKudFQLjfPP4fb6M/scene.splinecode' className={'!h-[calc(100%+60px)]'} />
				</div>
			</div>
		</div>
	);
}
