// utils
import { Typography } from '@tools/reactore';
import { useFormContext } from 'react-hook-form';
// components
import Input from '@/components-old/reactore/input';

// ----------------------------------------------------------------------

export default function EmailConfirmInformationsForm() {
	const {
		register,
		formState: { errors }
	} = useFormContext();

	return (
		<div className='flex h-full w-full flex-col justify-center space-y-6'>
			<Typography className='text-xl'>
				Pourriez-vous nous communiquer l’entité à laquelle vous appartenez ainsi que le <strong>poste occupé</strong> ?
			</Typography>
			<Input placeholder='Entité' name='entity' type='text' register={register} error={errors.entity?.message?.toString()} />
			<Input
				className='w-full'
				placeholder='Poste actuel'
				name='position'
				type='text'
				register={register}
				error={errors.position?.message?.toString()}
			/>
		</div>
	);
}
