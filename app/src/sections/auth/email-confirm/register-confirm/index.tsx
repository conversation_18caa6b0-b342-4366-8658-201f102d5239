// utils
import { Button, Typography } from '@tools/reactore';
import { useLottie } from 'lottie-react';
// sections
import animation from './bodybuilder-dancer.json';
// components
import AuthContainer from '@/components-old/layouts/auth-container';

// ----------------------------------------------------------------------

export default function EmailRegisterConfirm() {
	const options = {
		animationData: animation,
		loop: true
	};
	const { View } = useLottie(options);

	return (
		<AuthContainer>
			<div className='z-10 justify-center w-full mx-0 space-y-6 lg:w-159 lg:px-0'>
				<div className='flex flex-col items-center px-7 '>
					<div className='px-7 md:w-159.252'>{View}</div>
					<Typography className='pb-4 font-bold text-10'>Merci !</Typography>
					<Typography className='pb-8 text-xl font-normal text-center '>
						Nous reviendrons vers vous très prochainement !
					</Typography>
					<div className='px-7'>
						<Button
							href='login'
							className='py-3 bg-transparent border-black px-27 dark:border-gray-100 dark:text-gray-100 hover:dark:text-black'
							type='button'
							variant='outlined'>
							Terminer
						</Button>
					</div>
				</div>
			</div>
		</AuthContainer>
	);
}
