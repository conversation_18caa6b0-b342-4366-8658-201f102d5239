import { useState } from 'react';
// sections
import RegisterForm from './_components/register-form';
import SuccessSendEmail from './_components/success-send-email';
// components
import AuthContainer from '@/components-old/layouts/auth-container';

// ----------------------------------------------------------------------

export default function RegisterView() {
	const [sendMail, setSendMail] = useState<string | undefined>(undefined);

	const handleMailSubmit = (mail: string) => () => {
		setSendMail(mail);
	};

	return <AuthContainer>{sendMail ? <SuccessSendEmail email={sendMail} /> : <RegisterForm onSubmit={handleMailSubmit} />}</AuthContainer>;
}
