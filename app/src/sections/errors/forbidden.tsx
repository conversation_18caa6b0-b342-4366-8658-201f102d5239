// tanstack
import { useNavigate, useRouter } from '@tanstack/react-router';
// components
import { Button } from '@/components/ui/button';

// ----------------------------------------------------------------------

export default function ForbiddenError() {
	const navigate = useNavigate();

	const { history } = useRouter();

	return (
		<div className='h-svh'>
			<div className='m-auto flex h-full w-full flex-col items-center justify-center gap-2'>
				<h1 className='text-[7rem] font-bold leading-tight'>403</h1>
				<span className='font-medium'>Accès Refusé</span>
				<p className='text-center text-muted-foreground'>Désolé, vous n'êtes pas autorisé à accéder à cette page</p>
				<div className='mt-6 flex gap-4'>
					<Button variant='outline' onClick={() => history.go(-1)}>
						Retour
					</Button>
					<Button onClick={() => navigate({ to: '/' })}>Accueil</Button>
				</div>
			</div>
		</div>
	);
}
