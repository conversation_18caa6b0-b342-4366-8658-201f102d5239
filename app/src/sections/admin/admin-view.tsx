import { useMemo, useState } from 'react';
// utils
import { ContentSwitcher } from '@tools/reactore';
// hooks
import { useBoolean } from '@/hooks/use-boolean';
// contexts
import { useAuthContext } from '@/auth/auth-context';
// sections
import { DomainDialog, DomainTab } from './_components/domain';
import { JobDialog, JobTab } from './_components/job';
import { SpaceTab } from './_components/space';
import { SpaceDialog } from './_components/space/space-dialog';
import { StaffDialog, StaffTab } from './_components/staff';
import { TeamDialog, TeamTab } from './_components/team';
// components
import { PlusIcon } from '@/components-old/ui/icon';
import { CustomPageHeader } from '@/components/custom';

// ----------------------------------------------------------------------

export default function AdminView() {
	const { isAllowed } = useAuthContext();
	const isAdmin = isAllowed(['admin']);

	const [contentTabIndex, setContentTabIndex] = useState(0);

	const staffForm = useBoolean();
	const teamForm = useBoolean();
	const jobForm = useBoolean();
	const domainForm = useBoolean();
	const spaceForm = useBoolean();

	const handlePlusIconClick = () => {
		if (contentTabIndex === 0) {
			staffForm.onTrue();
		}
		if (contentTabIndex === 1) {
			teamForm.onTrue();
		}
		if (contentTabIndex === 2) {
			jobForm.onTrue();
		}
		if (contentTabIndex === 3) {
			domainForm.onTrue();
		}
		if (contentTabIndex === 4) {
			spaceForm.onTrue();
		}
	};

	const tabLabel = useMemo(() => {
		const returnTab = ['Utilisateurs'];

		if (isAdmin) {
			return [...returnTab, 'Équipes', 'Postes', 'Domaines', 'Espaces'];
		}
		return returnTab;
	}, [isAdmin]);

	return (
		<>
			<CustomPageHeader
				title='Administration'
				description='Utilisateurs, équipes, postes & domaines'
				rightContent={
					<>
						<ContentSwitcher
							titles={tabLabel}
							activeIndex={contentTabIndex}
							onClick={(index) => setContentTabIndex(index)}
							className='shadow-none'
						/>

						<button onClick={handlePlusIconClick} className='flex items-center justify-center bg-white rounded-md h-9 w-9'>
							<PlusIcon className='w-auto h-3 text-black' />
						</button>
					</>
				}
			/>

			<div className='flex-1 px-4 py-1 -mx-4 lg:flex-row lg:space-x-12 lg:space-y-0'>
				{contentTabIndex === 0 && <StaffTab />}
				{staffForm.value && <StaffDialog open={staffForm.value} onOpenChange={() => staffForm.onFalse()} />}

				{isAdmin && (
					<>
						{contentTabIndex === 1 && <TeamTab />}
						{contentTabIndex === 2 && <JobTab />}
						{contentTabIndex === 3 && <DomainTab />}
						{contentTabIndex === 4 && <SpaceTab />}

						{teamForm.value && <TeamDialog open={teamForm.value} onOpenChange={() => teamForm.onFalse()} />}
						{jobForm.value && <JobDialog open={jobForm.value} onOpenChange={jobForm.onFalse} />}
						{domainForm.value && <DomainDialog open={domainForm.value} onOpenChange={() => domainForm.onFalse()} />}
						{spaceForm.value && <SpaceDialog open={spaceForm.value} onOpenChange={() => spaceForm.onFalse()} />}
					</>
				)}
			</div>
		</>
	);
}
