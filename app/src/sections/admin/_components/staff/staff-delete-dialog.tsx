import { useCallback } from 'react';
// utils
import { IconAlertTriangle } from '@tabler/icons-react';
// api
import type { IStaffItem } from '@/api/interface';
import { useDeleteStaff } from '@/api/mutations';
// hooks
import { toast } from '@/hooks/use-toast';
// components
import { ConfirmDialog } from '@/components/confirm-dialog';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

// ----------------------------------------------------------------------

interface Props {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	currentRow: IStaffItem;
}

export function StaffDeleteDialog({ currentRow, open, onOpenChange }: Props) {
	const deleteStaff = useDeleteStaff();

	const handleDeleteRow = useCallback(
		async (uri: string) => {
			await deleteStaff.mutateAsync(uri, {
				onSuccess: () => {
					toast({
						variant: 'success',
						title: 'Utilisateur supprimé',
						description: `L'utilisateur ${currentRow.username} a bien été supprimé.`
					});
				},
				onError: () => {
					toast({
						variant: 'error',
						title: 'Erreur',
						description: `Une erreur s'est produite lors de la suppression de l'utilisateur ${currentRow.username}.`
					});
				}
			});
		},
		[currentRow]
	);

	return (
		<ConfirmDialog
			open={open}
			onOpenChange={onOpenChange}
			handleConfirm={() => handleDeleteRow(currentRow['@id'])}
			title={
				<span className='text-destructive'>
					<IconAlertTriangle className='inline-block mr-1 stroke-destructive' size={18} /> Suppression d'un utilisateur
				</span>
			}
			desc={
				<div className='space-y-4'>
					<p className='mb-2'>
						Êtes vous sûr de vouloir supprimer l'utilisateur <strong>{currentRow.username}</strong> ?
					</p>

					<Alert variant='destructive'>
						<AlertTitle>Attention!</AlertTitle>
						<AlertDescription>Cette action est irréversible.</AlertDescription>
					</Alert>
				</div>
			}
			confirmText='Delete'
			destructive
		/>
	);
}
