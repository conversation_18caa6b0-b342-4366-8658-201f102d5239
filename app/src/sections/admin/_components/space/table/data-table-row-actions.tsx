import { useState } from 'react';
// tanstack
import type { Row } from '@tanstack/react-table';
// utils
import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import { IconEdit, IconTrash } from '@tabler/icons-react';
// api
import type { ISpaceItem } from '@/api/interface';
// hooks
import { useBoolean } from '@/hooks/use-boolean';
// sections
import { SpaceDeleteDialog } from '../space-delete-dialog';
import { SpaceDialog } from '../space-dialog';
// components
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuShortcut,
	DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

// ----------------------------------------------------------------------

interface DataTableRowActionsProps {
	row: Row<ISpaceItem>;
}

export function DataTableRowActions({ row }: DataTableRowActionsProps) {
	const [selectedRow, setSelectedRow] = useState<ISpaceItem | undefined>(undefined);

	const spaceDialog = useBoolean();
	const spaceDeleteDialog = useBoolean();

	return (
		<>
			<DropdownMenu modal={false}>
				<DropdownMenuTrigger asChild>
					<Button variant='ghost' className='flex h-8 w-8 p-0 data-[state=open]:bg-muted'>
						<DotsHorizontalIcon className='h-4 w-4' />
						<span className='sr-only'>Ouvrir le menu</span>
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align='end' className='w-[160px]'>
					<DropdownMenuItem
						onClick={() => {
							setSelectedRow(row.original);
							spaceDialog.onTrue();
						}}>
						Modifier
						<DropdownMenuShortcut>
							<IconEdit size={16} />
						</DropdownMenuShortcut>
					</DropdownMenuItem>
					<DropdownMenuSeparator />
					<DropdownMenuItem
						onClick={() => {
							setSelectedRow(row.original);
							spaceDeleteDialog.onTrue();
						}}
						className='!text-red-500'>
						Supprimer
						<DropdownMenuShortcut>
							<IconTrash size={16} />
						</DropdownMenuShortcut>
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>

			{spaceDialog.value && selectedRow && (
				<SpaceDialog currentRow={selectedRow} open={spaceDialog.value} onOpenChange={spaceDialog.onToggle} />
			)}
			{spaceDeleteDialog.value && selectedRow && (
				<SpaceDeleteDialog currentRow={selectedRow} open={spaceDeleteDialog.value} onOpenChange={spaceDeleteDialog.onToggle} />
			)}
		</>
	);
}
