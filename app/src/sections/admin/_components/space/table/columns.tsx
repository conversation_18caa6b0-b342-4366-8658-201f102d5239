// tanstack
import type { ColumnDef } from '@tanstack/react-table';
// api
import type { ISpaceItem } from '@/api/interface';
// sections
import { DataTableRowActions } from './data-table-row-actions';
// components
import { DataTableColumnHeader } from '@/components/custom-table';

// ----------------------------------------------------------------------

const columns: ColumnDef<ISpaceItem>[] = [
	{
		accessorKey: 'name',
		header: ({ column }) => <DataTableColumnHeader column={column} title='Poste' />
	},
	{
		id: 'actions',
		header: 'Actions',
		cell: DataTableRowActions
	}
];

export { columns };
