import { useCallback } from 'react';
// utils
import { IconAlertTriangle } from '@tabler/icons-react';
// api
import type { IJobItem } from '@/api/interface';
import { useDeleteJob } from '@/api/mutations';
// hooks
import { toast } from '@/hooks/use-toast';
// components
import { ConfirmDialog } from '@/components/confirm-dialog';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

// ----------------------------------------------------------------------

interface Props {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	currentRow: IJobItem;
}

export function JobDeleteDialog({ currentRow, open, onOpenChange }: Props) {
	const deleteJob = useDeleteJob();

	const handleDeleteRow = useCallback(
		async (uri: string) => {
			await deleteJob.mutateAsync(uri, {
				onSuccess: () => {
					toast({
						variant: 'success',
						title: 'Poste supprimé',
						description: `Le poste ${currentRow.name} a bien été supprimé.`
					});
				},
				onError: () => {
					toast({
						variant: 'error',
						title: 'Erreur',
						description: `Une erreur s'est produite lors de la suppression du poste ${currentRow.name}.`
					});
				}
			});
		},
		[currentRow]
	);

	return (
		<ConfirmDialog
			open={open}
			onOpenChange={onOpenChange}
			handleConfirm={() => handleDeleteRow(currentRow['@id'])}
			title={
				<span className='text-destructive'>
					<IconAlertTriangle className='inline-block mr-1 stroke-destructive' size={18} /> Suppression d'un poste
				</span>
			}
			desc={
				<div className='space-y-4'>
					<p className='mb-2'>
						Êtes-vous sûr de vouloir supprimer le poste <span className='font-bold'>{currentRow.name}</span>?
					</p>

					<Alert variant='destructive'>
						<AlertTitle>Attention!</AlertTitle>
						<AlertDescription>Cette action est irréversible.</AlertDescription>
					</Alert>
				</div>
			}
			confirmText='Delete'
			destructive
		/>
	);
}
