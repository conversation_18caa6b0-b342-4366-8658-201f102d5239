// tanstack
import type { ColumnDef } from '@tanstack/react-table';
// utils
import { capitalizeCase } from '@/utils/change-case';
import { Tag } from '@tools/reactore';
import { UsersIcon } from 'lucide-react';
// api
import type { IJobItem } from '@/api/interface';
import { getJobTypeValue } from '@/api/enums';
// sections
import { DataTableRowActions } from './data-table-row-actions';
// components
import { DataTableColumnHeader } from '@/components/custom-table';

// ----------------------------------------------------------------------

const columns: ColumnDef<IJobItem>[] = [
	{
		accessorKey: 'name',
		header: ({ column }) => <DataTableColumnHeader column={column} title='Poste' />
	},
	{
		accessorKey: 'type',
		header: ({ column }) => <DataTableColumnHeader column={column} title='Type de poste' />,
		cell: ({ row }) => {
			const type = getJobTypeValue(row.getValue('type'));

			return <Tag className='bg-blue-50 text-free-state-info'>{capitalizeCase(type)}</Tag>;
		}
	},
	{
		accessorKey: 'memberCount',
		header: ({ column }) => <DataTableColumnHeader column={column} title='Utilisateurs' />,
		cell: ({ row }) => {
			const membersCount = row.getValue('memberCount') as number;

			return (
				<Tag className='flex space-x-2 text-purple-900 w-fit bg-purple-50'>
					<UsersIcon className='w-4 mr-1' /> {membersCount}
				</Tag>
			);
		},
		filterFn: (row, id, value) => {
			return value.includes(row.getValue(id));
		},
		enableSorting: false,
		enableHiding: false
	},
	{
		id: 'actions',
		header: 'Actions',
		cell: DataTableRowActions
	}
];

export { columns };
