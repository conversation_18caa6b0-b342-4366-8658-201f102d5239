import { useCallback, useState } from 'react';
// tanstack
import { useQuery } from '@tanstack/react-query';
// utils
import { Loading, Pagination } from '@tools/reactore';
// api
import type { ITeamItem } from '@/api/interface';
import { pawnQueryKeys } from '@/api/queries';
// sections
import { TeamRow } from './team-row';
// components
import Typography from '@/components/ui/typography';

// ----------------------------------------------------------------------

const defaultFilters = {
	page: 1,
	itemsPerPage: 10,
	name: '',
	'domain.name': 'all'
};

// ----------------------------------------------------------------------

export function TeamTab() {
	const [filters, setFilters] = useState(defaultFilters);

	const { data: teamsData, isError } = useQuery(pawnQueryKeys.pawn_team.all(filters));

	const handleChangeFilters = useCallback((name: string, value: string) => {
		setFilters((prevFilters) => ({ ...prevFilters, [name]: value }));
	}, []);

	if (teamsData) {
		return (
			<>
				<ul className='mt-6 space-y-4'>
					{teamsData?.['hydra:member'].map((team: ITeamItem) => (
						<TeamRow key={team['@id']} team={team} />
					))}
				</ul>
				<div className='flex justify-end pt-8'>
					{teamsData && (
						<Pagination
							data={teamsData}
							activePage={defaultFilters.page}
							setActivePage={(page) => handleChangeFilters('page', page.toString())}
							itemsPerPage={defaultFilters.itemsPerPage}
							setItemsPerPage={(itemsPerPage) => handleChangeFilters('itemsPerPage', itemsPerPage.toString())}
						/>
					)}
				</div>
			</>
		);
	}

	if (isError) {
		return (
			<div className='px-6 py-4 mt-6 space-y-4 rounded-3xl'>
				<Typography.Text className='text-free-state-error'>Erreur lors du chargement des espaces</Typography.Text>
			</div>
		);
	}

	return <Loading />;
}
