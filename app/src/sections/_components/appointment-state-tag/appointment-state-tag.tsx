// utils
import { getGenericEventStateWorkFlow } from '@/utils/common.utils';
import { capitalize } from 'lodash';
import { twJoin } from 'tailwind-merge';
// api
import type { IFiberAppointmentReport } from '@/api/interface';
import { EFiberAppointmentState, type IFiberAppointment } from '@/api/interface/fiber';
// components
import { Badge } from '@/components/ui/badge.tsx';

// ----------------------------------------------------------------------

const getLineColorFromState = (data?: string) => {
	switch (data) {
		case 'created':
			return 'bg-free-blue-200 text-blue-700';
		case 'blocked':
			return 'bg-free-orange-200 text-orange-700';
		case 'success':
			return 'bg-free-emerald-200 text-emerald-700';
		case 'error':
			return 'bg-free-red-100 text-red-700';
		default:
			return 'bg-free-gray-100 text-free-gray-700';
	}
};

type Props = {
	report?: IFiberAppointmentReport;
	state?: IFiberAppointment['state'];
};

export default function AppointmentStateTag({ report, state }: Props) {
	if ((state === 'PROCESSED_DEBRIEF' && report) || report) {
		const { mainCode, offCode, isSuccess } = report || {};

		return (
			<Badge className={twJoin(isSuccess ? 'bg-free-green-200 text-green-700' : 'bg-red-100 text-red-700')}>
				{isSuccess ? 'OK' : 'KO'} {mainCode}
				{' | '}
				{offCode}
			</Badge>
		);
	}
	if (state) {
		return (
			<Badge className={twJoin(getLineColorFromState(getGenericEventStateWorkFlow(state, 'appointments')))}>
				{capitalize(EFiberAppointmentState[state])}
			</Badge>
		);
	}

	return null;
}
