// tanstack
import { useQuery } from '@tanstack/react-query';
// utils
import { noDefine, WORKSITE_DIV } from '@/utils/common.utils';
import { spacePhoneNumber } from '@tools/reactore';
// api
import type { ICellFleetManager } from '@/api/interface/cell';
import { queries } from '@/api/queries';

// ----------------------------------------------------------------------

type Props = {
	fleetManager: ICellFleetManager;
};

export default function FleetManagerRow({ fleetManager }: Props) {
	const { data: user } = useQuery({
		...queries.cell_user.byId(fleetManager?.user?.['@id']),
		enabled: !!fleetManager?.user
	});
	const { data: contact } = useQuery({
		...queries.cell_contact.byId(user?.contact),
		enabled: !!user?.contact
	});

	const { firstName, lastName } = user || {};
	const { phone } = contact || {};

	return (
		<tr>
			<td>{noDefine((lastName || firstName) && `${lastName ?? ''} ${firstName ?? ''}`)}</td>
			<td>{WORKSITE_DIV}</td>
			<td>{noDefine(phone && spacePhoneNumber(phone))}</td>
		</tr>
	);
}
