import { useMemo } from 'react';
// utils
import type { TInputProps } from '@tools/reactore';
import { Input } from '@tools/reactore';
// api
import { ROLE_ITEMS } from '@/api/enums/pawn/role.enum';
// contexts
import { useAuthContext } from '@/auth/auth-context';

// ----------------------------------------------------------------------

type Props = {
	name?: string;
	type?: string;
} & Omit<TInputProps, 'name' | 'type'>;
export default function RolesInputSelect({ name, label, selectOptions, ...props }: Props) {
	const { isAllowed } = useAuthContext();

	const atr = useMemo(
		() => ({
			...props,
			name: name || 'roles',
			label: label || 'Rôle(s)'
		}),
		[props, name, label]
	);

	return (
		<Input
			className='col-span-1'
			{...atr}
			type='select'
			selectOptions={{
				isMultiple: true,
				className: 'max-h-40',
				items: ROLE_ITEMS.filter((roleItem) => {
					if (roleItem.value === 'ROLE_ADMIN' || roleItem.value === 'ROLE_REQUI') {
						return isAllowed(['admin']);
					}

					return true;
				}),
				...selectOptions
			}}
		/>
	);
}
