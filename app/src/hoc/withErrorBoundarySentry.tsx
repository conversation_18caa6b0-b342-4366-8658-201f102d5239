import * as Sentry from '@sentry/react'
import { ErrorBoundaryProps } from '@sentry/react'
import { ErrorComponent } from '@tanstack/react-router'
import { ComponentType, FC } from 'react'

export const withErrorBoundarySentry = <P extends Record<string, any>>(
	WrappedComponent: ComponentType<P>,
	errorBoundaryOptions: ErrorBoundaryProps = {}
): FC<P> => {
	return Sentry.withErrorBoundary(WrappedComponent, {
		...errorBoundaryOptions,
		fallback: ({ error }) => {
			return <ErrorComponent error={error} />
		}
	})
}
