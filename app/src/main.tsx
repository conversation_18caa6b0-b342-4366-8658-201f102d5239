import { StrictMode } from 'react';
// tanstack
import { QueryClientProvider } from '@tanstack/react-query';
// utils
import * as Sentry from '@sentry/react';
import ReactDOM from 'react-dom/client';
import { capitalize } from 'lodash';
import { He<PERSON><PERSON><PERSON>rovider } from 'react-helmet-async';
// contexts
import { AuthProvider } from './auth/auth-context';
// constants
import { globalEnv, PROJECT_BUILDER_DATE, PROJECT_NAME, PROJECT_VERSION } from '@/env';
// others
import App from './app';
import './index.scss';
import './sentry.client.config';
import { ThemeProvider } from './context/theme-context';
import { queryClient } from './services/query-client.service';

// ----------------------------------------------------------------------

const { APP_ENV, SENTRY_STATE, PUBLIC_CELL_API_URL, PUBLIC_PAWN_API_URL, PUBLIC_FIBER_API_URL, PUBLIC_MAGNET_API_URL } = globalEnv.env;

// registerSW({
// 	immediate: true,
// 	onOfflineReady() {}
// })

const charAt = `
||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
|
| ██████╗  	| PROJECT_NAME: 			${capitalize(PROJECT_NAME)}
| ██╔══██╗	| PROJECT_VERSION: 			${PROJECT_VERSION}
| ██████╔╝	| PROJECT_ENV: 				${APP_ENV}  
| ██╔══		| PROJECT_BUILDER_DATE: 	${PROJECT_BUILDER_DATE || 'n/a'}    
| ██║  		| SENTRY_STATE:				${SENTRY_STATE} 
| ╚═╝  	
|			 
||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
|
| PUBLIC_PAWN_API_URL:			${PUBLIC_PAWN_API_URL}
| PUBLIC_CELL_API_URL:			${PUBLIC_CELL_API_URL}
| PUBLIC_FIBER_API_URL:			${PUBLIC_FIBER_API_URL}
| PUBLIC_MAGNET_API_URL:		${PUBLIC_MAGNET_API_URL}
|
||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
`;

console.info(`%c${charAt}`, 'color: #7688eb; font-size: 12px;');

// Render the app
const rootElement = document.getElementById('root')!;
if (!rootElement.innerHTML) {
	const root = ReactDOM.createRoot(rootElement);
	root.render(
		<Sentry.ErrorBoundary>
			<StrictMode>
				<HelmetProvider>
					<QueryClientProvider client={queryClient}>
						<AuthProvider>
							<ThemeProvider defaultTheme='light' storageKey='pro-ui-theme'>
								<App />
							</ThemeProvider>
						</AuthProvider>
					</QueryClientProvider>
				</HelmetProvider>
			</StrictMode>
		</Sentry.ErrorBoundary>
	);
}
