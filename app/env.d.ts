/// <reference types="vite-plugin-svgr/client" />
/// <reference types="vite/client" />
/// <reference types="typescript" />

// <reference types="vite-plugin-pwa/react" />
// <reference types="vite-plugin-pwa/info" />


declare global {
	interface Window {
		env: {
			APP_ENV: string;
			PUBLIC_CELL_API_URL: string;
			PUBLIC_FIBER_API_URL: string;
			PUBLIC_MAGNET_API_URL: string;
			PUBLIC_PAWN_API_URL: string;
			SENTRY_STATE: string;
		};
	}
	declare const window: Window & typeof globalThis
}

interface ImportMetaEnv {
	readonly APP_ENV: 'production' | 'development';
	readonly NODE_ENV: 'production' | 'development';
	readonly VITE_SERVER_URL: string;
	readonly VITE_ASSET_URL: string;
	readonly VITE_STATIC_EXPORT: string;
	readonly VITE_PROJECT_ID: string;
	readonly VITE_PROJECT_NAME: string;
	readonly VITE_PROJECT_VERSION: string;
	readonly VITE_PROJECT_DEPLOYMENT: string;
	readonly VITE_PROJECT_ENV: string;
	readonly VITE_DEVTOOLS_JOTAI: string;
	readonly VITE_DEVTOOLS_TANSTACK_QUERY: string;
	readonly VITE_DEVTOOLS_TANSTACK_ROUTER: string;
	readonly VITE_DEVTOOLS_REACT_QUERY_REWIND: string;
	readonly VITE_LOGIN: string;
	readonly VITE_NODE_ENV: 'production' | 'development';
	readonly VITE_APP_ENV: 'production' | 'development';
	readonly VITE_DEVTOOLS_REACT_QUERY: 'enabled' | 'disabled';
	readonly VITE_MOCKING: 'enabled' | 'disabled';
	readonly VITE_BUILD_CHECK: 'enabled' | 'disabled';
	readonly VITE_MOCKING_LOG: 'enabled' | 'disabled';
	readonly VITE_ANALYZE: 'enabled' | 'disabled';
	readonly VITE_SENTRY: 'enabled' | 'disabled';
}

export interface ImportMeta {
	readonly env: ImportMetaEnv;
}
