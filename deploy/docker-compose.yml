networks:
  local-proteam:
    external: true
  local-front:
    external: true

services:
  ###########
  # Base app containers
  ###########
    app:
      image: ${APP_NAME}/app:${IMG_TAG:-local}
      tty: true # allow colors in term
      profiles: ["base"]
      environment:
        IMG_TAG: ${IMG_TAG:-0.0.0} # Allowed: local, swarm, kube
        BUILDER_VERSION: "0.0.0" # Allowed: local, swarm, kube
        APP_ENV: "local" # Allowed: dev, pbe, int, prod
        DEPLOY_TYPE: local # Allowed: local, swarm, kube
        GL_TOKEN: ${GL_TOKEN}
        SENTRY_STATE: "disabled"
#        SENTRY_AUTH_TOKEN: ${SENTRY_AUTH_TOKEN} # Allowed: local, swarm, kube
        PUBLIC_PAWN_API_URL: "https://pawn.pbe.b2b.iliad.fr"
        PUBLIC_MAGNET_API_URL: "https://magnet.pbe.b2b.iliad.fr"
        PUBLIC_CELL_API_URL: "https://cell.pbe.b2b.iliad.fr"
        PUBLIC_FIBER_API_URL: "https://fiber.pbe.b2b.iliad.fr"
      ports:
        - '**********:8080:80'
        - "**********:5443:443/tcp"
        - "**********:3001:3000"
      networks:
        - local-proteam
        - local-front
      volumes:
        - ${ROOT_PATH}/app/:/srv/app:rw
        - ${ROOT_PATH}/scripts/:/scripts:ro
        # nginx
        - ${ROOT_PATH}/containers/app/nginx/nginx_base.conf:/etc/nginx/extends/nginx_base.conf:ro
        - ${ROOT_PATH}/app/certs:/etc/nginx/certs:ro
        - ${ROOT_PATH}/containers/app/nginx/nginx_local.conf:/etc/nginx/conf.d/default.conf:ro
        - ${ROOT_PATH}/containers/app/nginx/nginx_core.conf:/etc/nginx/nginx.conf:ro


      