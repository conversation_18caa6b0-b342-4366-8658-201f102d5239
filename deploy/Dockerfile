ARG NODE_VERSION NGINX_VERSION

########################################################################################################################
##################################### NODE ####################################
########################################################################################################################

FROM harbor.adm.internal.proteam.iliad.fr/dockerio-cache/node:${NODE_VERSION}-alpine AS node-base

ARG BUILDER_VERSION BUILDER_DATE
ARG SENTRY_STATE SENTRY_AUTH_TOKEN SENTRY_ORG SENTRY_PROJECT SENTRY_URL
ARG SENTRY_LOG_LEVEL=info

ARG PUBLIC_PAWN_API_URL PUBLIC_MAGNET_API_URL
ARG WITH_COMPILATION WITH_DEBUGGING GL_TOKEN APP_ENV

ENV WITH_COMPILATION=${WITH_COMPILATION} WITH_DEBUGGING=${WITH_DEBUGGING} GL_TOKEN=${GL_TOKEN} APP_ENV=${APP_ENV}
ENV PROJECT_BUILDER_DATE=${BUILDER_DATE} PROJECT_BUILDER_VERSION=${BUILDER_VERSION}
ENV SENTRY_STATE=${SENTRY_STATE} SENTRY_AUTH_TOKEN=${SENTRY_AUTH_TOKEN} SENTRY_ORG=${SENTRY_ORG} SENTRY_PROJECT=${SENTRY_PROJECT} SENTRY_URL=${SENTRY_URL} SENTRY_LOG_LEVEL=${SENTRY_LOG_LEVEL}
ENV BUILDER_DATE=${BUILDER_DATE} BUILDER_VERSION=${BUILDER_VERSION}

WORKDIR /srv/app

COPY ./app .

RUN set -eux; \
    apk --update add --no-cache \
        bash \
        openssl \
    ; \
    corepack enable; \
    corepack prepare pnpm@10 --activate; \
    pnpm config set store-dir .pnpm-store; \
    pnpm config set "@tools:registry=https://gitlab.adm.internal.proteam.iliad.fr/api/v4/groups/10/-/packages/npm/"; \
    pnpm config set "//gitlab.adm.internal.proteam.iliad.fr/api/v4/projects/112/packages/npm/:_authToken=${GL_TOKEN}"; \
    pnpm config set "//gitlab.adm.internal.proteam.iliad.fr/api/v4/projects/10/packages/npm/:_authToken=${GL_TOKEN}"; \
    pnpm config set "//gitlab.adm.internal.proteam.iliad.fr/api/v4/groups/10/-/packages/npm/:_authToken=${GL_TOKEN}"; \
    pnpm config set "engine-strict=false"; \
    pnpm install;

CMD ["pnpm","dev"]

########################################################################################################################
########################################################################################################################

########################################################################################################################
##################################### NODE Build ####################################
########################################################################################################################

FROM node-base AS node-builder

ENV NODE_ENV=production

RUN pnpm build
#RUN pnpm build && pnpm sentry

########################################################################################################################
########################################################################################################################


########################################################################################################################
#################################### NGNIX ###############################################
########################################################################################################################

FROM harbor.adm.internal.proteam.iliad.fr/dockerio-cache/nginx:${NGINX_VERSION}-alpine AS nginx

ARG WITH_COMPILATION WITH_DEBUGGING
ARG BUILDER_DATE BUILDER_VERSION

ENV BUILDER_DATE=${BUILDER_DATE} BUILDER_VERSION=${BUILDER_VERSION}
ENV WITH_COMPILATION=${WITH_COMPILATION} WITH_DEBUGGING=${WITH_DEBUGGING}

WORKDIR /srv/app

COPY --from=node-builder --chmod=755 --chown=nginx /srv/app/dist /build
COPY --chmod=444 scripts/ /scripts
COPY VERSION.txt /VERSION.txt

COPY --chmod=444 containers/app/nginx/nginx_base.conf /etc/nginx/extends/nginx_base.conf
COPY --chmod=444 containers/app/nginx/nginx.conf /etc/nginx/conf.d/default.conf
COPY --chmod=444 containers/app/nginx/nginx_core.conf /etc/nginx/nginx.conf
#COPY --chmod=444 containers/app/nginx/certs /etc/nginx/certs


RUN set -eux; \
    apk --update add --no-cache \
    python3 \
    libmaxminddb \
    nginx-mod-http-geoip \
    bash \
    curl \
    nodejs \
    ; \
    mkdir -p /var/lib/nginx /var/log/app /var/log/nginx /var/cache/nginx; \
    mkdir -p /etc/nginx/variables; \
    wget -q -O- https://dl.miyuru.lk/geoip/maxmind/country/maxmind.dat.gz | gunzip -c > /var/lib/nginx/maxmind-country.dat; \
    wget -q -O- https://dl.miyuru.lk/geoip/maxmind/city/maxmind.dat.gz | gunzip -c > /var/lib/nginx/maxmind-city.dat; \
    chown -R nginx /var/lib/nginx /var/log/app /var/log/nginx /var/cache/nginx; \
    chmod -R 766 /var/lib/nginx /var/log/app /var/log/nginx /var/cache/nginx; \
    chown -R nginx:nginx /etc/nginx/conf.d; \
    touch /var/run/nginx.pid; \
    chown -R nginx:nginx /var/run/nginx.pid; \
    sync;

EXPOSE 80

ENTRYPOINT ["node","/scripts/entrypoint.js"]
CMD ["nginx", "-g", "daemon off;"]

#################################### NGNIX Local ###############################################

FROM harbor.adm.internal.proteam.iliad.fr/dockerio-cache/nginx:${NGINX_VERSION}-alpine AS nginx-local

ARG WITH_COMPILATION WITH_DEBUGGING
ARG BUILDER_DATE BUILDER_VERSION

ENV BUILDER_DATE=${BUILDER_DATE} BUILDER_VERSION=${BUILDER_VERSION}
ENV WITH_COMPILATION=${WITH_COMPILATION} WITH_DEBUGGING=${WITH_DEBUGGING}

WORKDIR /srv/app

COPY --from=node-builder --chmod=755 --chown=nginx /srv/app/dist /build
COPY --chmod=444 scripts/ /scripts
COPY VERSION.txt /VERSION.txt

COPY --chmod=444 containers/app/nginx/nginx_base.conf /etc/nginx/extends/nginx_base.conf
COPY --chmod=444 containers/app/nginx/nginx_local.conf /etc/nginx/conf.d/default.conf
COPY --chmod=444 containers/app/nginx/nginx_core.conf /etc/nginx/nginx.conf
COPY --chmod=444 app/certs /etc/nginx/certs


RUN set -eux; \
    apk --update add --no-cache \
    python3 \
    libmaxminddb \
    nginx-mod-http-geoip \
    bash \
    curl \
    nodejs \
    ; \
    mkdir -p /var/lib/nginx /var/log/app /var/log/nginx /var/cache/nginx; \
    mkdir -p /etc/nginx/variables; \
    wget -q -O- https://dl.miyuru.lk/geoip/maxmind/country/maxmind.dat.gz | gunzip -c > /var/lib/nginx/maxmind-country.dat; \
    wget -q -O- https://dl.miyuru.lk/geoip/maxmind/city/maxmind.dat.gz | gunzip -c > /var/lib/nginx/maxmind-city.dat; \
    chown -R nginx /var/lib/nginx /var/log/app /var/log/nginx /var/cache/nginx; \
    chmod -R 766 /var/lib/nginx /var/log/app /var/log/nginx /var/cache/nginx; \
    chown -R nginx:nginx /etc/nginx/conf.d; \
    touch /var/run/nginx.pid; \
    chown -R nginx:nginx /var/run/nginx.pid; \
    sync;

EXPOSE 80

ENTRYPOINT ["node","/scripts/entrypoint.js"]
CMD ["nginx", "-g", "daemon off;"]

########################################################################################################################
########################################################################################################################

FROM layer AS end-layer
ARG BUILDER_DATE BUILDER_VERSION
ENV BUILDER_DATE=${BUILDER_DATE} BUILDER_VERSION=${BUILDER_VERSION}
# Long term : Add USER to all layers here