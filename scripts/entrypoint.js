const { spawn } = require("child_process");
const fs = require("fs");

console.info(`
# ========================================
# Welcome to our main application entrypoint
# This script is run when a container is launched right before starting main pid
# Most scripts are in the entrypoints folder and called with "check $script_name"
# ========================================
`);

console.info("# Defining some helpers and base variables");

const start = new Date();
//
// check() {
//     exec=$1
//     shift
//     source "/scripts/entrypoints/$exec.sh" "$@"
// }

console.info("# Displaying start phrase", start.getTime());
console.info(
  "\n\033[1;33m=============%s\n  Starting %s  \n=============%s\033[0m\n\n",
);

console.info(`# Starting checklist
# =======================================
`);

console.info("\nRunning entrypoint checklist\n============================\n");

const params = process.argv;
const XSENTRY_TOKEN = process.env.XSENTRY_TOKEN?.length ? process.env.XSENTRY_TOKEN : "not value"

console.info("\n");
console.info("=========================");
console.info(`Preload checklist done in "${new Date().getTime()} - ${start}`);

console.info("=========================");
console.info(
  "\n\033[1;32mStarting CMD process as pid 1\033[0m:\n  -> %s\n\n---\n",
);
// console.info(JSON.stringify(params,null,2));
console.info("\033[0m");

const argParam = params.slice(2);
const envArr = Object.entries(process.env);
const envSav = [
    'APP_ENV',
    'PUBLIC_PAWN_API_URL',
    'PUBLIC_MAGNET_API_URL',
    'PUBLIC_FIBER_API_URL',
    'PUBLIC_CELL_API_URL',
    'SENTRY_STATE',
]
const envWindow = {};

envArr
    .filter(([key]) => envSav.includes(key))
    .forEach(([keyEnv,valueEnv]) => {
    envWindow[keyEnv] = valueEnv;
  });

console.info(JSON.stringify(envWindow,null,2));

fs.writeFileSync(
  "/build/env.js",
  `window.env = ${JSON.stringify(envWindow, null, 2)}`,
);
console.info("# Set varible in ngnix config");
console.log("XSENTRY_TOKEN:", XSENTRY_TOKEN.slice(0,3)+XSENTRY_TOKEN.slice(3,XSENTRY_TOKEN.length).replaceAll(/./g,"*"));

fs.writeFileSync('/etc/nginx/variables/nginx_variable.conf', `set $xsentry_token "${XSENTRY_TOKEN}";`);

console.info("==================================================");
console.info("==================================================");

// Aff
spawn(argParam[0], argParam.slice(1), {
  stdio: ["inherit", "inherit", "inherit"],
});
